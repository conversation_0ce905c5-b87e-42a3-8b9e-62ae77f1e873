import { defineConfig } from '@umijs/max';
import proxy from './config/proxy';
import routes from './config/routes';

const { REACT_APP_ENV } = process.env;

export default defineConfig({
  npmClient: 'pnpm',
  outputPath: 'dist',
  dva: {},
  proxy: proxy[REACT_APP_ENV || 'sandbox'],
  routes,
  antd: {},
  tailwindcss: {},
  title: 'PETKIT Care+',
  // analyze: {
  //   analyzerMode: 'static',
  //   analyzerPort: '15100',
  // },
  // moment2dayjs: {
  //   preset: 'antd',
  //   plugins: ['duration'],
  // },
  locale: {
    // default: 'en',
    default: 'zh-CN',
    baseNavigator: false,
  },
  base: '/web-bs/',
  publicPath: '/web-bs/',
  styles: ['//at.alicdn.com/t/c/font_3769886_1xmwnvrf3cy.css'],
  metas: [
    { name: 'viewport', content: 'width=device-width, viewport-fit=cover' },
  ],
  chainWebpack: (config) => {
    config.optimization.splitChunks({
      chunks: 'async',
      minSize: 30000,
      minChunks: 2,
      maxSize: 0,
      automaticNameDelimiter: '.',
      // name: true,
      // maxAsyncRequests: 30,
      // maxInitialRequests: 10,
      cacheGroups: {
        vendor: {
          // 基本框架
          name: 'vendors',
          test: /^.*node_modules[\\/](?!react|react-dom|antd).*$/,
          chunks: 'all',
          priority: 10,
        },
        // echartsVenodr: {
        //     // 异步加载echarts包
        //     name: 'echartsVenodr',
        //     test: /(echarts|zrender)/,
        //     chunks: 'async',
        //     priority: 10, // 高于async-commons优先级
        // },
        'async-commons': {
          // 其余异步加载包
          chunks: 'async',
          minChunks: 2,
          name: 'async-commons',
          priority: 9,
        },
        commons: {
          // 其余同步加载包
          chunks: 'all',
          minChunks: 2,
          name: 'commons',
          priority: 8,
        },
      },
    });
  },
  headScripts: [
    {
      content: `
      (function(w, d, s, q, i) {
        w[q] = w[q] || [];
        var f = d.getElementsByTagName(s)[0],j = d.createElement(s);
        j.async = true;
        j.id = 'beacon-aplus';
        j.src = 'https://d.alicdn.com/alilog/mlog/aplus/' + i + '.js';
        f.parentNode.insertBefore(j, f);
       })(window, document, 'script', 'aplus_queue', '203467608');

       var appKey = origin.indexOf('api.petkit.com') > -1 ? '64a54241a1a164591b43dd91' : '64a7bf39a1a164591b46d68a';
       var origin = window.location.origin;
       var aplus_queue = window.aplus_queue;
       //集成应用的appKey
       aplus_queue.push({
         action: 'aplus.setMetaInfo',
         arguments: ['appKey', appKey],
       });

       /* 如果使用的是单页面应用，例如：React、Vue、Angular等，则需要添加下面的代码 */
       /* 关闭自动PV发送，如果不是单页面应用，请删掉下方代码 */
       aplus_queue.push({
         action: 'aplus.setMetaInfo',
         arguments: ['aplus-waiting', 'MAN']
       });

       //是否开启调试模式
       aplus_queue.push({
         action: 'aplus.setMetaInfo',
         arguments: ['DEBUG', true]
       });
      `,
    },
  ],
});
