// module.exports = {
//   moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx'],
//   moduleNameMapper: {
//     '^@/(.*)$': '<rootDir>/src/$1',
//   },
//   transform: {
//     '^.+\\.tsx?$': 'ts-jest',
//   },
//   testMatch: ['src/__tests__/**/*.spec.(js|jsx|ts|tsx)'],
// };

const dayjs = require('dayjs');
const duration = require('dayjs/plugin/duration');

dayjs.extend(duration);

global.dayjs = dayjs;
