{"0 debug pnpm:scope": {"selected": 1}, "1 debug pnpm:package-manifest": {"initial": {"name": "com-petkit-bs-mobile-web", "private": true, "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "scripts": {"build": "pnpm lint && max build", "f2elint-commit-file-scan": "pnpx f2elint commit-file-scan", "f2elint-fix": "pnpx f2elint fix", "f2elint-scan": "pnpx f2elint scan", "postinstall": "max setup", "lint": "pnpm f2elint-fix && pnpm f2elint-scan", "setup": "max setup", "start": "max dev", "start:local": "cross-env REACT_APP_ENV=local MOCK=none pnpm start", "start:mock": "cross-env REACT_APP_ENV=mock MOCK=none pnpm start", "start:online": "cross-env REACT_APP_ENV=online MOCK=none pnpm start", "start:pre": "cross-env REACT_APP_ENV=pre pnpm start", "start:sandbox": "cross-env REACT_APP_ENV=sandbox MOCK=none pnpm start", "start:test": "cross-env REACT_APP_ENV=test MOCK=none pnpm start"}, "husky": {"hooks": {"pre-commit": "f2elint commit-file-scan", "commit-msg": "f2elint commit-msg-scan"}}, "dependencies": {"@mantas/request": "^2.0.5", "@umijs/max": "^4.0.29", "antd-mobile": "^5.25.1"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "cross-env": "^7.0.3", "f2elint": "^3.0.0", "typescript": "^4.1.2", "tailwindcss": "^3"}}, "prefix": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web"}, "2 debug pnpm:context": {"currentLockfileExists": false, "storeDir": "/Users/<USER>/.pnpm-store/v6/v3", "virtualStoreDir": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web/node_modules/.pnpm"}, "3 debug pnpm:stage": {"prefix": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "stage": "resolution_started"}, "4 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/@types/react/18.0.25", "wanted": {"dependentId": ".", "name": "@types/react", "rawSpec": "^18.0.0"}}, "5 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/@types/react-dom/18.0.8", "wanted": {"dependentId": ".", "name": "@types/react-dom", "rawSpec": "^18.0.0"}}, "6 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/cross-env/7.0.3", "wanted": {"dependentId": ".", "name": "cross-env", "rawSpec": "^7.0.3"}}, "7 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/f2elint/3.0.0", "wanted": {"dependentId": ".", "name": "f2elint", "rawSpec": "^3.0.0"}}, "8 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/typescript/4.8.4", "wanted": {"dependentId": ".", "name": "typescript", "rawSpec": "^4.1.2"}}, "9 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/@mantas/request/2.0.5", "wanted": {"dependentId": ".", "name": "@mantas/request", "rawSpec": "^2.0.5"}}, "10 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/@umijs/max/4.0.29", "wanted": {"dependentId": ".", "name": "@umijs/max", "rawSpec": "^4.0.29"}}, "11 debug pnpm:_dependency_resolved": {"resolution": "npm.petkit.com/antd-mobile/5.25.1", "wanted": {"dependentId": ".", "name": "antd-mobile", "rawSpec": "^5.25.1"}}, "12 debug pnpm:progress": {"packageId": "npm.petkit.com/@types/react-dom/18.0.8", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "found_in_store"}, "13 debug pnpm:progress": {"packageId": "npm.petkit.com/@types/react-dom/18.0.8", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "resolved"}, "14 debug pnpm:progress": {"packageId": "npm.petkit.com/@types/react/18.0.25", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "found_in_store"}, "15 debug pnpm:progress": {"packageId": "npm.petkit.com/@types/react/18.0.25", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "resolved"}, "16 debug pnpm:progress": {"packageId": "npm.petkit.com/cross-env/7.0.3", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "found_in_store"}, "17 debug pnpm:progress": {"packageId": "npm.petkit.com/cross-env/7.0.3", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "resolved"}, "18 debug pnpm:progress": {"packageId": "npm.petkit.com/@mantas/request/2.0.5", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "found_in_store"}, "19 debug pnpm:progress": {"packageId": "npm.petkit.com/@mantas/request/2.0.5", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "resolved"}, "20 debug pnpm:progress": {"packageId": "npm.petkit.com/typescript/4.8.4", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "found_in_store"}, "21 debug pnpm:progress": {"packageId": "npm.petkit.com/typescript/4.8.4", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "resolved"}, "22 debug pnpm:progress": {"packageId": "npm.petkit.com/@umijs/max/4.0.29", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "found_in_store"}, "23 debug pnpm:progress": {"packageId": "npm.petkit.com/@umijs/max/4.0.29", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "resolved"}, "24 debug pnpm:progress": {"packageId": "npm.petkit.com/f2elint/3.0.0", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "found_in_store"}, "25 debug pnpm:progress": {"packageId": "npm.petkit.com/f2elint/3.0.0", "requester": "/Users/<USER>/oms-workspace/com-petkit-bs-mobile-web", "status": "resolved"}, "26 error pnpm": {"code": "ERR_PNPM_NO_MATCHING_VERSION", "packageMeta": {"name": "detective", "versions": {"0.0.0": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "dependencies": {"burrito": "0.2.x"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@0.0.0", "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "ee9734f295d887bfeb35e01fa20cc4058c14be2c", "tarball": "http://npm.petkit.com/detective/-/detective-0.0.0.tgz"}}, "0.0.1": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "dependencies": {"burrito": "0.2.x"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "702413d1d9fbb879da942313936879d3a4ace810", "tarball": "http://npm.petkit.com/detective/-/detective-0.0.1.tgz"}}, "0.0.2": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "dependencies": {"burrito": "0.2.x"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "b1c16267b4b8aff2076c29a05bcbc1981c117987", "tarball": "http://npm.petkit.com/detective/-/detective-0.0.2.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.0.3": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "dependencies": {"burrito": "0.2.x"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@0.0.3", "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "c51e7d02b328676ef801e971019fda3ac78266b0", "tarball": "http://npm.petkit.com/detective/-/detective-0.0.3.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.0.4": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "dependencies": {"burrito": "0.2.x"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "detective@0.0.4", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "fca0be0bbae435ef756aadb14c7837776b851cea", "tarball": "http://npm.petkit.com/detective/-/detective-0.0.4.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.1.0": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"uglify-js": "~1.2.5"}, "devDependencies": {"tap": "~0.2.3"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "detective@0.1.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "79a8248574d2c44951389a90650cf4ce5e5e6cbf", "tarball": "http://npm.petkit.com/detective/-/detective-0.1.0.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.1.1": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.1.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"uglify-js": "~1.2.5"}, "devDependencies": {"tap": "~0.2.3"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "detective@0.1.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "f1e04fe973754c8907ae51edd3e230e380d76fe9", "tarball": "http://npm.petkit.com/detective/-/detective-0.1.1.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.2.0": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.2.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "~0.9.9"}, "devDependencies": {"tap": "~0.2.6"}, "engines": {"node": ">=0.6.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@0.2.0", "dist": {"shasum": "d46b5eb799ea82e51b8788f1ae37098b63119409", "tarball": "http://npm.petkit.com/detective/-/detective-0.2.0.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.2.1": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "0.2.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "~0.9.9"}, "devDependencies": {"tap": "~0.2.6"}, "engines": {"node": ">=0.6.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@0.2.1", "dist": {"shasum": "9ce92601fd223810c29432ad034f8c62d8b8654f", "tarball": "http://npm.petkit.com/detective/-/detective-0.2.1.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "1.1.0": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "~1.0.2"}, "devDependencies": {"tap": "~0.2.6"}, "engines": {"node": ">=0.6.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@1.1.0", "dist": {"shasum": "bdf3ca6f7e5d7c17b746031f24fc193fba7b51f8", "tarball": "http://npm.petkit.com/detective/-/detective-1.1.0.tgz"}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "2.0.0": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "1.0.2", "escodegen": "0.0.15"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@2.0.0", "dist": {"shasum": "5a47720e6ad59b763abb960be90e9e74a09d3cf9", "tarball": "http://npm.petkit.com/detective/-/detective-2.0.0.tgz"}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "detective", "description": "Find all calls to require() no matter how crazily nested using a proper walk of the AST", "version": "2.1.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "1.0.2", "escodegen": "0.0.15"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@2.1.0", "dist": {"shasum": "d92c8bc4fdc4f887abcf8347edca38093842a028", "tarball": "http://npm.petkit.com/detective/-/detective-2.1.0.tgz"}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "2.1.1": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "2.1.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "1.0.2", "escodegen": "0.0.15"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@2.1.1", "dist": {"shasum": "4f2e4407bc49280e5a1bc546586c786db88887d5", "tarball": "http://npm.petkit.com/detective/-/detective-2.1.1.tgz"}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "2.1.2": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "2.1.2", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "1.0.2", "escodegen": "0.0.15"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "detective@2.1.2", "dist": {"shasum": "d22ad9f18c82efb3f55fee2e244883da6bbb8e37", "tarball": "http://npm.petkit.com/detective/-/detective-2.1.2.tgz"}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "2.2.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "2.2.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "1.0.2", "escodegen": "0.0.15"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "_id": "detective@2.2.0", "dist": {"shasum": "f18d277271ddff287d39070c7dea96e07dae64be", "tarball": "http://npm.petkit.com/detective/-/detective-2.2.0.tgz"}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "2.3.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "2.3.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"esprima": "1.0.2", "escodegen": "0.0.15"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective", "_id": "detective@2.3.0", "dist": {"shasum": "21e7d2ca80f1af9291b8ffe4ca09b1a9dbdb5ae3", "tarball": "http://npm.petkit.com/detective/-/detective-2.3.0.tgz"}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "2.4.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "2.4.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"escodegen": "0.0.15", "esprima-six": "0.0.3"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective", "_id": "detective@2.4.0", "dist": {"shasum": "28eb67756051963d34fb8e2eb39322a115451a75", "tarball": "http://npm.petkit.com/detective/-/detective-2.4.0.tgz"}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "2.4.1": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "2.4.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"escodegen": "~1.1.0", "esprima-six": "0.0.3"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective", "_id": "detective@2.4.1", "dist": {"shasum": "943b8eec4c346f6ac501de25da2ea1a57ed77819", "tarball": "http://npm.petkit.com/detective/-/detective-2.4.1.tgz"}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"escodegen": "~1.1.0", "esprima": "~1.0.4"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective", "_id": "detective@3.0.0", "dist": {"shasum": "e4bce70624ed12200fad0aef0166a901db0eb3b0", "tarball": "http://npm.petkit.com/detective/-/detective-3.0.0.tgz"}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "3.1.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "3.1.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"escodegen": "~1.1.0", "esprima-fb": "3001.1.0-dev-harmony-fb"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective", "_id": "detective@3.1.0", "dist": {"shasum": "77782444ab752b88ca1be2e9d0a0395f1da25eed", "tarball": "http://npm.petkit.com/detective/-/detective-3.1.0.tgz"}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "4.0.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.0.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "~0.9.0", "defined": "0.0.0", "escodegen": "^1.4.1"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "80124f1228354d378d7b0979c584b11fc0c273b8", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective", "_id": "detective@4.0.0", "_shasum": "9ffdb5555ddb1571fdbdc6f4ceac08e5e4cf8467", "_from": ".", "_npmVersion": "2.1.3", "_nodeVersion": "0.10.31", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "9ffdb5555ddb1571fdbdc6f4ceac08e5e4cf8467", "tarball": "http://npm.petkit.com/detective/-/detective-4.0.0.tgz"}, "directories": {}}, "4.0.1": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.0.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "0.0.0", "escodegen": "^1.4.1"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "00851b64d0a2b97891406aa306cdddd09c0d16f6", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective", "_id": "detective@4.0.1", "_shasum": "5cd79d9b333ca178528b4cc85d0d99f95e36af59", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "1.6.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "5cd79d9b333ca178528b4cc85d0d99f95e36af59", "tarball": "http://npm.petkit.com/detective/-/detective-4.0.1.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "4.0.2": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.0.2", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "0.0.0", "escodegen": "^1.4.1"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "75946ee23cb7abb974a2053235ecb2663b879c07", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.0.2", "_shasum": "eca2990cf7c603272b75d0bf6430149b45180da5", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "eca2990cf7c603272b75d0bf6430149b45180da5", "tarball": "http://npm.petkit.com/detective/-/detective-4.0.2.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "directories": {}}, "4.0.3": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.0.3", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "^1.0.0", "escodegen": "^1.4.1"}, "devDependencies": {"tap": "^1.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "3888d8b92fe529df32be76c41632d14a0c388400", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.0.3", "_shasum": "6b6289ed545fec2bac52be30006eb79694a312d2", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.10.38", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "6b6289ed545fec2bac52be30006eb79694a312d2", "tarball": "http://npm.petkit.com/detective/-/detective-4.0.3.tgz"}, "directories": {}}, "4.1.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.1.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "^1.0.0", "escodegen": "^1.4.1"}, "devDependencies": {"tap": "^1.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "a220299ec75d99302ccca3647c928932a7b18fc1", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.1.0", "_shasum": "7e8c5189bfb429bf392041e94ebe0599382aecac", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.10.38", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "7e8c5189bfb429bf392041e94ebe0599382aecac", "tarball": "http://npm.petkit.com/detective/-/detective-4.1.0.tgz"}, "directories": {}}, "4.1.1": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.1.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "^1.0.0", "escodegen": "^1.4.1"}, "devDependencies": {"tap": "^1.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "3f03dd4cf14e6d91a0eb9e322663065939fa73fe", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.1.1", "_shasum": "9c4bac1e9fb8bb34f7f18cae080ea1d03aff2cda", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "9c4bac1e9fb8bb34f7f18cae080ea1d03aff2cda", "tarball": "http://npm.petkit.com/detective/-/detective-4.1.1.tgz"}, "directories": {}}, "4.2.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.2.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "^1.0.0", "escodegen": "^1.4.1"}, "devDependencies": {"tap": "^1.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "c8e5be71ab28764ba306fa4e29702b37e4c924f9", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.2.0", "_shasum": "1617d85a5a526c0e6ed6e460b9daee84f72ce9b4", "_from": ".", "_npmVersion": "2.13.2", "_nodeVersion": "2.5.0", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "1617d85a5a526c0e6ed6e460b9daee84f72ce9b4", "tarball": "http://npm.petkit.com/detective/-/detective-4.2.0.tgz"}, "directories": {}}, "4.3.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.3.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "^1.0.0"}, "devDependencies": {"tap": "^1.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "bc632274e168839f8c2fadeb1f03532f362e0d00", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.3.0", "_shasum": "100413a34bb09e13785b08b56341b29ed949c734", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "100413a34bb09e13785b08b56341b29ed949c734", "tarball": "http://npm.petkit.com/detective/-/detective-4.3.0.tgz"}, "directories": {}}, "4.3.1": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.3.1", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^1.0.3", "defined": "^1.0.0"}, "devDependencies": {"tap": "^1.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "aa1700035cabe9c5ec7cdbab5be79f8fdb9e20be", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.3.1", "_shasum": "9fb06dd1ee8f0ea4dbcc607cda39d9ce1d4f726f", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "zertosh", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "dist": {"shasum": "9fb06dd1ee8f0ea4dbcc607cda39d9ce1d4f726f", "tarball": "http://npm.petkit.com/detective/-/detective-4.3.1.tgz"}, "directories": {}}, "4.3.2": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.3.2", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^3.1.0", "defined": "^1.0.0"}, "devDependencies": {"tap": "^5.7.1"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "d2d25a009e5ad5a14ee04be602457bf3e35bb192", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.3.2", "_shasum": "77697e2e7947ac3fe7c8e26a6d6f115235afa91c", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "6.3.1", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "77697e2e7947ac3fe7c8e26a6d6f115235afa91c", "tarball": "http://npm.petkit.com/detective/-/detective-4.3.2.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/detective-4.3.2.tgz_1477162754693_0.34135371283628047"}, "directories": {}}, "4.4.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.4.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^4.0.3", "defined": "^1.0.0"}, "devDependencies": {"tap": "^5.7.1"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "aaacebb32b051444ab70211336a88643a1c7872f", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.4.0", "_shasum": "544672a27ca5a8a6d8b1453bf2eff3f047c993e9", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "6.3.1", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "544672a27ca5a8a6d8b1453bf2eff3f047c993e9", "tarball": "http://npm.petkit.com/detective/-/detective-4.4.0.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/detective-4.4.0.tgz_1488407726235_0.7974571010563523"}, "directories": {}}, "4.5.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.5.0", "repository": {"type": "git", "url": "git://github.com/substack/node-detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^4.0.3", "defined": "^1.0.0"}, "devDependencies": {"tap": "^5.7.1"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "4aa3713807feb699f18d83152e5475d0ec5345b6", "bugs": {"url": "https://github.com/substack/node-detective/issues"}, "homepage": "https://github.com/substack/node-detective#readme", "_id": "detective@4.5.0", "_shasum": "6e5a8c6b26e6c7a254b1c6b6d7490d98ec91edd1", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "6.3.1", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "6e5a8c6b26e6c7a254b1c6b6d7490d98ec91edd1", "tarball": "http://npm.petkit.com/detective/-/detective-4.5.0.tgz"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/detective-4.5.0.tgz_1488408199426_0.11118059908039868"}, "directories": {}}, "4.6.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.6.0", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^5.2.1", "defined": "^1.0.0"}, "devDependencies": {"tap": "^10.7.3"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "07ea3c5c8c15e933296c49c609d644d96d888e11", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@4.6.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-bvuiWqtm2RYtEnfjAuRw9XTJhUbfbOfsmtIRXQcNgMyUplxJP611EzoVxObkSvaSnfBUEjoKVzaUzdtIRMcCXg==", "shasum": "d1a793ad0bcc829fa225465061096b7bca040527", "tarball": "http://npm.petkit.com/detective/-/detective-4.6.0.tgz"}, "maintainers": [{"email": "<EMAIL>", "name": "zertosh"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "feross"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective-4.6.0.tgz_1511723385092_0.04981517908163369"}, "directories": {}}, "4.7.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.7.0", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^5.2.1", "defined": "^1.0.0"}, "devDependencies": {"tap": "^10.7.3"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "8d576483c74efaa0c01816d3366d74cf1bbdb6e9", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@4.7.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4mBqSEdMfBpRAo/DQZnTcAXenpiSIJmVKbCMSotS+SFWWcrP/CKM6iBRPdTiEO+wZhlfEsoZlGqpG6ycl5vTqw==", "shasum": "6276e150f9e50829ad1f90ace4d9a2304188afcf", "tarball": "http://npm.petkit.com/detective/-/detective-4.7.0.tgz"}, "maintainers": [{"email": "<EMAIL>", "name": "zertosh"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "feross"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective-4.7.0.tgz_1512138985539_0.7308002102654427"}, "directories": {}}, "4.7.1": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "4.7.1", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^5.2.1", "defined": "^1.0.0"}, "devDependencies": {"tap": "^10.7.3"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "9aa713028adf2bb4ce7bed690e1029d9fb88faa1", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@4.7.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-H6PmeeUcZloWtdt4DAkFyzFL94arpHr3NOwwmVILFiy+9Qd4JTxxXrzfyGk/lmct2qVGBwTSwSXagqu2BxmWig==", "shasum": "0eca7314338442febb6d65da54c10bb1c82b246e", "tarball": "http://npm.petkit.com/detective/-/detective-4.7.1.tgz"}, "maintainers": [{"email": "<EMAIL>", "name": "zertosh"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "pk<PERSON><PERSON>"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "feross"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective-4.7.1.tgz_1513853011244_0.39407283812761307"}, "directories": {}}, "5.0.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "5.0.0", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "main": "index.js", "keywords": ["require", "source", "analyze", "ast"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"acorn": "^5.2.1", "acorn5-object-spread": "^5.0.0", "defined": "^1.0.0"}, "devDependencies": {"tap": "^10.7.3"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "5af17627ab34bc9f250ee6e5f498429d8df0f821", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@5.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "bret", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vVmCzTirqV81zEfL4CsYBmzoCudHvBRlZcjssPf/l/hCq/IAA2PAJgEPYZCSS/LX39sD433YEv4Gijg9bKjQAA==", "shasum": "a25cd44db941a8c9c26bed94f7f520c7113916a5", "tarball": "http://npm.petkit.com/detective/-/detective-5.0.0.tgz"}, "maintainers": [{"email": "<EMAIL>", "name": "zertosh"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "pk<PERSON><PERSON>"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "feross"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective-5.0.0.tgz_1514862731791_0.18281852547079325"}, "directories": {}}, "5.0.1": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "5.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "dependencies": {"acorn": "^5.2.1", "acorn5-object-spread": "^5.0.0", "defined": "^1.0.0"}, "devDependencies": {"tap": "^10.7.3"}, "engines": {"node": ">=4.0.0"}, "keywords": ["analyze", "ast", "require", "source"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "scripts": {"test": "tap test/*.js"}, "gitHead": "9a9cf1c0bcc8109c63554c7d471e29e802e173b2", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@5.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "bret", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dP38zI4CR5OQDahCcBIrskhscv+5x+7+fHipBdOK9gpXc8LKrSAv4fz7IQVpcwdLDU38aA2E9qsGLRZ4L5GTpg==", "shasum": "864348b5c9ffc6000592b034fc10f46c758eb955", "tarball": "http://npm.petkit.com/detective/-/detective-5.0.1.tgz"}, "maintainers": [{"email": "<EMAIL>", "name": "zertosh"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "pk<PERSON><PERSON>"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "feross"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective-5.0.1.tgz_1514864215327_0.9009296323638409"}, "directories": {}}, "5.0.2": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "5.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "dependencies": {"acorn": "^5.2.1", "@browserify/acorn5-object-spread": "^5.0.1", "defined": "^1.0.0"}, "devDependencies": {"tap": "^10.7.3"}, "engines": {"node": ">=0.8.0"}, "keywords": ["analyze", "ast", "require", "source"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "scripts": {"test": "tap test/*.js"}, "gitHead": "6d6baaebebd51bd3d4b374c28d6ad931521ce411", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@5.0.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "bret", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NUsLoezj4wb9o7vpxS9F3L5vcO87ceyRBcl48op06YFNwkyIEY997JpSCA5lDlDuDc6JxOtaL5qfK3muoWxpMA==", "shasum": "84ec2e1c581e74211e2ae4ffce1edf52c3263f84", "tarball": "http://npm.petkit.com/detective/-/detective-5.0.2.tgz"}, "maintainers": [{"email": "<EMAIL>", "name": "zertosh"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "tehshrike"}, {"email": "<EMAIL>", "name": "pk<PERSON><PERSON>"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "feross"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective-5.0.2.tgz_1515266635857_0.4522130098193884"}, "directories": {}}, "5.1.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "5.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": {"detective": "bin/detective.js"}, "dependencies": {"acorn-node": "^1.3.0", "defined": "^1.0.0", "minimist": "^1.1.1"}, "devDependencies": {"tap": "^10.7.3"}, "engines": {"node": ">=0.8.0"}, "keywords": ["analyze", "ast", "require", "source"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "scripts": {"test": "tap test/*.js"}, "gitHead": "3ec100f56fc568675a6e33cd14314953822a45d5", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@5.1.0", "_npmVersion": "5.7.1", "_nodeVersion": "9.6.1", "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TFHMqfOvxlgrfVzTEkNBSh9SvSNX/HfF4OFI2QFGCyPm02EsyILqnUeb5P6q7JZ3SFNTBL5t2sePRgrN4epUWQ==", "shasum": "7a20d89236d7b331ccea65832e7123b5551bb7cb", "tarball": "http://npm.petkit.com/detective/-/detective-5.1.0.tgz", "fileCount": 41, "unpackedSize": 19590}, "maintainers": [{"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "dcousens"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "feross"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "pk<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "tehshrike"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "zertosh"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective_5.1.0_1519812164844_0.9201311874339202"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "detective", "description": "find all require() calls by walking the AST", "version": "5.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": {"detective": "bin/detective.js"}, "dependencies": {"acorn-node": "^1.6.1", "defined": "^1.0.0", "minimist": "^1.1.1"}, "devDependencies": {"tap": "^10.7.3"}, "engines": {"node": ">=0.8.0"}, "keywords": ["analyze", "ast", "require", "source"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "scripts": {"test": "tap test/*.js"}, "gitHead": "1c3837acfb68162faa6ab417ad0139dd20af50b2", "bugs": {"url": "https://github.com/browserify/detective/issues"}, "homepage": "https://github.com/browserify/detective#readme", "_id": "detective@5.2.0", "_nodeVersion": "11.8.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-6SsIx+nUUbuK0EthKjv0zrdnajCCXVYGmbYYiYjFVpzcjwEs/JMDZ8tPRG29J/HhN56t3GJp2cGSWDRjjot8Pg==", "shasum": "feb2a77e85b904ecdea459ad897cc90a99bd2a7b", "tarball": "http://npm.petkit.com/detective/-/detective-5.2.0.tgz", "fileCount": 44, "unpackedSize": 20571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTssFCRA9TVsSAnZWagAA55kP/A9HQnOFUPc5XWLdLNHw\nTe48iIa52WGxazjyYkrUlpzWqqONtagCyW10Ccojs33yOBz6EuWRbr/GXWWC\npAO67apCdLBxKiagiM8TPapm4QvP3K0arMzP4mbch5YTwF34KovborZugDJe\nXksYSKYIxB7qtBB9Iwc1MKSd8m6eYaRM1FTkAKhPQfAGY/iyyyD089z/M1bZ\ncb+VGejC8Zld/p/H0dwlsC3AoAd4Be3tgW12T07uzZ1+QbYyq5bKDvrTGaYb\nawRi468WdSoz6oNnXQpsBdSALhX6MnAkT4EzBY7qLuj12BaivN+8JtBv46Jl\n2+/iGiM1DD6/nlovYldtFltFRh0X/RUQ3FKn2zn9Lfs93G4DHyWS072GhEU9\npJVlc8YxzcvmDIEYI1f5ywqrpXfAwynno6lkRgDVxHfkxO6jMZsG1OntzwkQ\nGF8zY5kFdkVSgSKXeuGvUuBKNq/+bZVYQ9lyO8k0yfTwyV//tOm9qUtGjhM/\nuo8E9PclqyzdTxTVSF30AsFhNtQArBcsHXcQvwT4X2bE+d3sne3U84YxVLuq\nj3NWWzg38TA52JGOJJN6YDBxN1YviZNBOp79oSG0R6GUkBvabj7MuafqccLk\nAVYqCDN5DRbhgFAu3ZAIhT34eBd1nbB2GM0rXsZtdPZDLit/J7Ap2S41ryco\nPGS4\r\n=XoY8\r\n-----END PGP SIGNATURE-----\r\n"}, "maintainers": [{"email": "micha<PERSON>.<EMAIL>", "name": "ahdin<PERSON>ur"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ashaffer88"}, {"email": "<EMAIL>", "name": "bal<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bret"}, {"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "dominictarr"}, {"email": "<EMAIL>", "name": "el<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "feross"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fpereira1"}, {"email": "<EMAIL>", "name": "garann"}, {"email": "<EMAIL>", "name": "gkatsev"}, {"email": "<EMAIL>", "name": "goto-bus-stop"}, {"email": "<EMAIL>", "name": "hughsk"}, {"email": "<EMAIL>", "name": "indutny"}, {"email": "<EMAIL>", "name": "jmm"}, {"email": "jp<PERSON><PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jryans"}, {"email": "<EMAIL>", "name": "leichtgewicht"}, {"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "matt<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "maxogden"}, {"email": "<EMAIL>", "name": "mellowmelon"}, {"email": "<EMAIL>", "name": "parshap"}, {"email": "<EMAIL>", "name": "pk<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "maoch<PERSON><PERSON>@gmail.com", "name": "ste<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "tehshrike"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thlorenz"}, {"email": "<EMAIL>", "name": "ungoldman"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "zertosh"}], "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detective_5.2.0_1548667652804_0.152066386411168"}, "_hasShrinkwrap": false}}, "dist-tags": {"latest": "5.2.0"}, "time": {"modified": "2022-04-28T11:00:15.926Z", "created": "2011-06-18T01:14:25.709Z", "5.2.0": "2019-01-28T09:27:32.906Z", "5.1.0": "2018-02-28T10:02:44.891Z", "5.0.2": "2018-01-06T19:23:55.931Z", "5.0.1": "2018-01-02T03:36:55.463Z", "5.0.0": "2018-01-02T03:12:11.871Z", "4.7.1": "2017-12-21T10:43:32.195Z", "4.7.0": "2017-12-01T14:36:26.460Z", "4.6.0": "2017-11-26T19:09:46.003Z", "4.5.0": "2017-03-01T22:43:21.349Z", "4.4.0": "2017-03-01T22:35:28.181Z", "4.3.2": "2016-10-22T18:59:16.871Z", "4.3.1": "2015-11-01T07:17:07.967Z", "4.3.0": "2015-11-01T06:54:08.099Z", "4.2.0": "2015-08-20T05:27:02.720Z", "4.1.1": "2015-07-01T12:37:47.649Z", "4.1.0": "2015-05-25T03:32:56.156Z", "4.0.3": "2015-05-25T00:07:23.183Z", "4.0.2": "2015-05-07T17:36:14.049Z", "4.0.1": "2015-04-25T20:20:29.417Z", "4.0.0": "2014-11-19T11:44:22.828Z", "3.1.0": "2014-03-18T22:54:10.326Z", "3.0.0": "2014-03-05T01:30:47.039Z", "2.4.1": "2014-02-28T08:59:31.676Z", "2.4.0": "2014-02-01T05:29:12.283Z", "2.3.0": "2014-01-20T18:46:42.035Z", "2.2.0": "2013-09-14T18:39:14.513Z", "2.1.2": "2013-05-21T23:40:05.291Z", "2.1.1": "2013-04-17T20:26:25.430Z", "2.1.0": "2013-03-31T02:28:03.727Z", "2.0.0": "2013-03-02T01:16:15.918Z", "1.1.0": "2013-02-25T00:30:43.609Z", "0.2.1": "2012-08-21T20:17:46.953Z", "0.2.0": "2012-07-30T14:40:45.012Z", "0.1.1": "2012-04-30T07:12:52.972Z", "0.1.0": "2012-03-05T22:32:39.946Z", "0.0.4": "2011-11-26T02:55:57.478Z", "0.0.3": "2011-09-10T05:26:03.694Z", "0.0.2": "2011-08-03T22:37:08.300Z", "0.0.1": "2011-06-20T01:22:33.001Z", "0.0.0": "2011-06-18T01:14:26.407Z"}, "_rev": "12-237d23c1407dfd06", "readme": "# detective\n\nfind all calls to `require()` by walking the AST\n\n[![build status](https://secure.travis-ci.org/browserify/detective.png)](http://travis-ci.org/browserify/detective)\n\n# example\n\n## strings\n\nstrings_src.js:\n\n``` js\nvar a = require('a');\nvar b = require('b');\nvar c = require('c');\n```\n\nstrings.js:\n\n``` js\nvar detective = require('detective');\nvar fs = require('fs');\n\nvar src = fs.readFileSync(__dirname + '/strings_src.js');\nvar requires = detective(src);\nconsole.dir(requires);\n```\n\noutput:\n\n```\n$ node examples/strings.js\n[ 'a', 'b', 'c' ]\n```\n\n# methods\n\n``` js\nvar detective = require('detective');\n```\n\n## detective(src, opts)\n\nGive some source body `src`, return an array of all the `require()` calls with\nstring arguments.\n\nThe options parameter `opts` is passed along to `detective.find()`.\n\n## var found = detective.find(src, opts)\n\nGive some source body `src`, return `found` with:\n\n* `found.strings` - an array of each string found in a `require()`\n* `found.expressions` - an array of each stringified expression found in a\n`require()` call\n* `found.nodes` (when `opts.nodes === true`) - an array of AST nodes for each\nargument found in a `require()` call\n\nOptionally:\n\n* `opts.word` - specify a different function name instead of `\"require\"`\n* `opts.nodes` - when `true`, populate `found.nodes`\n* `opts.isRequire(node)` - a function returning whether an AST `CallExpression`\nnode is a require call\n* `opts.parse` - supply options directly to\n[acorn](https://npmjs.org/package/acorn) with some support for esprima-style\noptions `range` and `loc`\n* `opts.ecmaVersion` - default: 9\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install detective\n```\n\n# license\n\nMIT", "_id": "detective", "_attachments": {}, "cachedAt": 1667902882358}, "pkgsStack": [], "err": {"name": "pnpm", "message": "No matching version found for detective@^5.2.1", "code": "ERR_PNPM_NO_MATCHING_VERSION", "stack": "pnpm: No matching version found for detective@^5.2.1\n    at resolveNpm (/opt/homebrew/lib/node_modules/pnpm/dist/pnpm.cjs:87315:15)\n    at processTicksAndRejections (internal/process/task_queues.js:95:5)\n    at async Object.resolve (/opt/homebrew/lib/node_modules/pnpm/dist/pnpm.cjs:87499:34)\n    at async run (/opt/homebrew/lib/node_modules/pnpm/dist/pnpm.cjs:75414:23)"}}}