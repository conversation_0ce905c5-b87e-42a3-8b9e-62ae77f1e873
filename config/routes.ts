// import { cn, en, LocaleEnum } from '../src/locale';

export interface Route {
  /**
   * Any valid URL path
   */
  path?: string;
  /**
   * A React component to render only when the location matches.
   */
  component?: string | (() => any);
  wrappers?: string[];
  /**
   * navigate to a new location
   */
  redirect?: string;
  /**
   * When true, the active class/style will only be applied if the location is matched exactly.
   */
  exact?: boolean;
  routes?: Route[];
  [k: string]: any;
}

const routes: Route[] = [
  {
    path: '/',
    routes: [
      {
        path: '/',
        redirect: '/redirect',
      },
      {
        path: '/redirect',
        routes: [
          {
            path: '/redirect',
            component: '@/pages/Redirect',
          },
          {
            path: '/redirect/:type',
            component: '@/pages/Redirect',
          },
        ],
      },
      {
        path: '/package',
        routes: [
          {
            path: '/package/multiple-device-plan',
            component: '@/pages/Package/MultipleDevicePlan/Home',
          },
          {
            path: '/package/bundle-sku/edit',
            component: '@/pages/Package/BundleSku/Edit',
          },
        ],
      },
      {
        path: '/package-selector',
        component: '@/pages/Package/Selector',
      },
      {
        path: '/payment/result',
        component: '@/pages/Package/Payment/Result',
      },
      {
        path: '/subscription/result',
        component: '@/pages/Package/Subscription/Result',
      },
      {
        path: '/receive/result',
        component: '@/pages/Package/Receive/Result',
      },
      {
        path: '/more-benefit',
        component: '@/pages/Package/Sku/MoreBenefitList',
      },
      {
        path: '/order/list',
        component: '@/pages/Order/List',
      },
      {
        path: '/description/:type',
        component: '@/pages/CloudService/Description',
      },
      // {
      //   path: '/entrance',
      //   component: '@/pages/Entrance',
      // },
      {
        path: '/instruction',
        routes: [
          {
            path: '/instruction/daily-highlights/:deviceType',
            component: '@/pages/Instruction/DailyHighlights',
          },
        ],
      },

      // 开通提醒页面
      {
        path: '/mention',
        component: '@/pages/Package/Mention',
      },
    ],
  },
];

export default routes;
