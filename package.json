{"name": "com-petkit-bs-mobile-web", "private": true, "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "scripts": {"build": "max build", "postinstall": "max setup", "setup": "max setup", "start": "max dev", "start:local": "pnpx cross-env REACT_APP_ENV=local pnpm start", "start:mock": "pnpx cross-env REACT_APP_ENV=mock pnpm start", "start:online": "pnpx cross-env REACT_APP_ENV=online pnpm start", "start:pre": "pnpx cross-env REACT_APP_ENV=pre pnpm start", "start:sandbox": "pnpx cross-env REACT_APP_ENV=sandbox pnpm start", "test": "jest"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.0", "@mantas/request": "^2.10.0", "@umijs/max": "^4.0.29", "@umijs/utils": "^4.0.42", "ahooks": "^3.7.2", "antd-mobile": "^5.25.1", "antd-mobile-icons": "^0.3.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.2", "dayjs": "^1.11.6", "detective": "^5.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "ramda": "^0.28.0", "react-html-parser": "^2.0.2", "vconsole": "^3.15.0"}, "devDependencies": {"@types/jest": "^29.5.4", "@types/lodash": "^4.17.6", "@types/ramda": "^0.28.20", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-html-parser": "^2.0.2", "cross-env": "^7", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "tailwindcss": "^3", "ts-jest": "^29.1.1", "typescript": "^5.0.3"}, "engines": {"node": ">= 14.0.0"}}