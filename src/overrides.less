@import url('./assets/styles/variables');

.adm {
  &-card {
    &-header {
      &-title {
        width: 100%;
      }
    }
  }

  &-button {
    &-disabled {
      border-color: @default-color;
      color: @default-color;
    }
  }
}

.adm-tabs {
  &-header {
    border-bottom: 0;
  }

  &-tab {
    color: @default-color;
    font-size: 1.125rem;

    &-line {
      background-color: @price-color !important;
    }

    &-active {
      color: @active-color;
    }

    &-wrapper {
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  &-content {
    padding: 12px 0;
  }
}

.adm-capsule-tabs {
  &-tab {
    background: transparent;
    color: @default-color;

    &-active {
      background: #fff0ba;
      color: @active-color;
    }

    &-wrapper {
      flex-grow: 0;
    }
  }

  &-header {
    border-bottom: 0;
  }

  &-content {
    padding: 12px 0;
  }
}

// Toast提示样式调整
.adm-toast-mask .adm-toast-main-icon .adm-toast-icon {
  display: flex;
  justify-content: center;
}

.custom-duration-switch {
  .adm-switch-checkbox {
    background-color: @primary-color !important;

    &::before {
      background-color: @primary-color !important;
    }
  }
}