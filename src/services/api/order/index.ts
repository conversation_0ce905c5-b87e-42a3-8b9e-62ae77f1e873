import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const orderApiOption: Record<
  | 'orderList'
  | 'weixinOrderCreation'
  | 'alipayOrderCreation'
  | 'orderCreation'
  | 'orderCreationV2'
  | 'cancelOrder'
  | 'orderPayAgain'
  | 'subscriptionSign'
  | 'subscriptionSignV2'
  | 'paymentResult'
  | 'orderDetail'
  | 'subscribeResult'
  // 涉及到补差价/降级的计算结果
  | 'orderCalc'
  // 涉及到补差价/降级的签约计算结果
  | 'subscriptionOrderCalc'
  // 判断当前所选套餐是否存在未支付的情况
  | 'orderStateCheck',
  RequestOption
> = {
  // 获取Order列表
  orderList: {
    url: '/bs/order/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 微信创建订单
  /**
   * @deprecated
   */
  weixinOrderCreation: {
    url: '/bs/order/createWeixin',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 支付宝创建订单
  /**
   * @deprecated
   */
  alipayOrderCreation: {
    url: '/bs/order/createAlipay',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  /**
   * @deprecated 发起支付V1
   */
  orderCreation: {
    url: '/bs/order/pay',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  /**
   * @description 发起支付V2
   */
  orderCreationV2: {
    url: '/bs/order/pay/v2',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 取消订单
  cancelOrder: {
    url: '/bs/order/cancel',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 再次支付订单
  orderPayAgain: {
    url: '/bs/order/repay/v2',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 同步订单支付结果
  paymentResult: {
    url: '/bs/order/syncPaymentResult',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取订单详情
  orderDetail: {
    url: '/bs/order/get',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 涉及到补差价/降级的计算结果
  orderCalc: {
    url: '/bs/order/calc',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 签约支付宝 - 周期扣款
  subscriptionSign: {
    url: '/bs/subscription/sign',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  subscriptionSignV2: {
    url: '/bs/subscription/sign/v2',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 同步签约结果
  subscribeResult: {
    url: '/bs/subscription/sync',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 涉及补差价/降级的签约计算结果
  subscriptionOrderCalc: {
    url: '/bs/subscription/calc',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 判断当前所选套餐是否存在未支付的情况
  orderStateCheck: {
    url: '/bs/order/state/check',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
};
