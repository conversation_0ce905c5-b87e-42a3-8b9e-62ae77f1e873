import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const deviceApiOption: Record<'deviceInfoWithPackage', RequestOption> = {
  // 获取deviceInfoWithPackage
  deviceInfoWithPackage: {
    url: '/bs/device/detail/v2',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // deviceInfoWithPackage: {
  //   url: '/bs/device/detail',
  //   option: {
  //     ...defaultOptions,
  //     method: RequestMethod.Get,
  //   },
  // },
};
