import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const utilsApiOption: Record<
  | 'utilsList'
  | 'utilsCreation'
  | 'utilsUpdate'
  | 'utilsDetail'
  | 'utilsDeletion'
  | 'getSixtyFreeActivityTime',
  RequestOption
> = {
  // 获取Utils列表
  utilsList: {
    url: '/utils/list',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 创建Utils
  utilsCreation: {
    url: '/utils',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 更新Utils
  utilsUpdate: {
    url: '/utils',
    option: {
      method: RequestMethod.Put,
    },
  },
  // 根据Utils的id获取详情数据
  utilsDetail: {
    url: '/utils',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 删除Utils
  utilsDeletion: {
    url: '/utils',
    option: {
      method: RequestMethod.Delete,
    },
  },
  // 获取Util列表
  getSixtyFreeActivityTime: {
    url: '/bs/config/getSixtyFreeActivityTime',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
};
