import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const productApiOption: Record<
  | 'suitableProductList'
  | 'benefitListByDevice'
  | 'productDetail'
  | 'benefitCompareDetail'
  | 'defaultBenefitList'
  | 'bundleSkuList'
  | 'minPriceBundleSku',
  RequestOption
> = {
  // 获取Product列表
  suitableProductList: {
    url: '/bs/product/sku/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取商品SKU的权益信息
  benefitListByDevice: {
    url: '/bs/product/sku/benefit/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  productDetail: {
    url: '/bs/product/sku/detail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取权益对比接口
  benefitCompareDetail: {
    url: '/bs/product/sku/benefit/compare/detail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取默认权益列表（H5权益对比）
  defaultBenefitList: {
    url: '/bs/product/sku/benefit/defaultList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取组合SKU列表
  bundleSkuList: {
    url: '/bs/product/sku/bundle/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取最低价格的组合SKU
  minPriceBundleSku: {
    url: '/bs/product/sku/bundle/minPrice',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
};
