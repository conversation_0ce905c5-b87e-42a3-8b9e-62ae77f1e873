import { useNavigate } from '@umijs/max';
import React, { useEffect } from 'react';

// import { AppLanguageTypes } from '@/locales';
import { AppLanguageTypes } from '@/locales';
import { EnterEnum, UrlStatusEnum } from '@/models/common.interface';
import { SuitableDeviceTypeEnum } from '@/models/product/interface';
import global from '@/utils/global';
import useUrlState from '@ahooksjs/use-url-state';
import { ErrorBlock, SpinLoading } from 'antd-mobile';
import { DescriptionTypeEnum } from '../CloudService/interface';
// import { OrderStatusEnum } from '@/models/order/interface';

enum PageTypeEnum {
  PACKAGE_SELECTOR = 'package-selector',
  ORDER_LIST = 'order-list',
  ENTRANCE = 'entrance',
  // instruction该枚举值和route是绑定的，不可修改
  INSTRUCTION = 'instruction',
  PROTOCOL = 'protocol',
}

enum DeviceFunctionEnum {
  DAILY_HIGHLIGHTS = 'daily-highlights',
}

interface UrlParam {
  token: string;
  deviceId?: string;
  deviceType?: SuitableDeviceTypeEnum;
  pageType?: PageTypeEnum;
  language?: AppLanguageTypes;
  Enter?: EnterEnum;
  userId?: string;
  // 功能说明页面参数
  functionName?: DeviceFunctionEnum;
  // 协议页面参数
  protocolName?: DescriptionTypeEnum;
  // 是否有页面自己的顶部导航栏和标题栏
  hasNavBar?: UrlStatusEnum;
}

const Redirect: React.FC = () => {
  const imageSize = 60;
  const navigate = useNavigate();
  const [urlParam] = useUrlState<UrlParam>();
  // useEffect(() => {
  //   if (!urlRestParams || !urlRestParams.type) return;
  //   console.log(urlRestParams);
  // }, [urlRestParams]);

  useEffect(() => {
    if (!urlParam) return;

    const {
      token = '',
      deviceId,
      deviceType,
      pageType = PageTypeEnum.PACKAGE_SELECTOR,
      language = 'zh_CN',
      Enter = EnterEnum.UNKNOWN,
      userId = 'unknown',
      functionName,
      protocolName,
      hasNavBar = UrlStatusEnum.ENABLE,
      // orderId,
      // subNo,
      // state,
    } = urlParam as UrlParam;

    // 设置token
    global.saveToken(token || '');

    // 设置语言
    global.saveLangauge(language ? language : 'zh_CN');

    // 设置埋点入口信息
    global.saveEnter(Enter);

    global.saveUserInfo(userId);

    // 缓存设备信息
    global.saveDeviceInfo({ deviceId, deviceType });

    // 初始化 友盟统计
    // console.log('before aplus init');
    // global.aplus.init({ userId, deviceType });
    global.aplus.sendPV();

    // console.log(location.href, deviceId);

    if (pageType === PageTypeEnum.PACKAGE_SELECTOR && deviceId && deviceType) {
      navigate(
        `/package-selector?deviceId=${deviceId}&deviceType=${deviceType}`,
        {
          replace: true,
        },
      );
    } else if (pageType === PageTypeEnum.ENTRANCE) {
      navigate(`/description/${DescriptionTypeEnum.INTRODUCTION}?hasNavBar=0`, {
        replace: true,
      });
    } else if (pageType === PageTypeEnum.PROTOCOL && protocolName) {
      navigate(`/description/${protocolName}?hasNavBar=0`, { replace: true });
    } else if (pageType === PageTypeEnum.ORDER_LIST) {
      navigate(`/order/list`, { replace: true });
    } else if (pageType === PageTypeEnum.INSTRUCTION) {
      if (functionName && deviceType)
        navigate(
          `/${pageType}/${functionName}/${deviceType}?hasNavBar=${hasNavBar}`,
          {
            replace: true,
          },
        );
    }
  }, [urlParam]);

  return (
    <ErrorBlock
      className="flex flex-col items-center justify-center"
      fullPage
      style={{
        '--image-height': `${imageSize}px`,
        '--image-height-full-page': `${imageSize}px`,
      }}
      image={<SpinLoading style={{ '--size': `${imageSize}px` }} />}
      title={''}
      description={'正在加载中，请稍候...'}
    />
  );
};

export default Redirect;
