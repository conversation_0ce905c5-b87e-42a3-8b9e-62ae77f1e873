import PriceInfo from '@/components/PriceInfo';
import { NumberBooleanEnum } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import { DeviceInfoWithPackage } from '@/models/device/interface';
import {
  fetchOrderCalc,
  fetchOrderCreationV2,
  fetchOrderStateCheck,
  fetchPaymentResult,
  fetchSubscribeResult,
  fetchSubscriptionOrderCalc,
} from '@/models/order/fetch';
import {
  OrderCalcParam,
  OrderCreationV2Param,
  OrderStatusEnum,
  PayTypeEnum,
  SubscriptionOrderCalcParam,
  SubscriptionStateEnum,
} from '@/models/order/interface';
import { ProductSku } from '@/models/product/interface';
import { DescriptionTypeEnum } from '@/pages/CloudService/interface';
import { CalcResult } from '@/pages/interface';
import {
  transferOrderCalcResultToCalcResult,
  transferSubscriptionCalcResultToCalcResult,
} from '@/pages/util';
import { AplusPayResultTypeEnum, PointTypeEnum } from '@/utils/aplus';
import global from '@/utils/global';
import { history, useDispatch, useSelector } from '@umijs/max';
import { Dialog, NavBar, Popup, Toast } from 'antd-mobile';
import { AntOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import React, { useEffect, useMemo, useState } from 'react';
import Confirm from '../Confirm';
import DeviceInfo from '../Selector/DeviceInfo';
import ActivationWay from './ActivationWay';
import CurrentServiceInfo from './CurrentServiceInfo';
import NextOrEqualLevelServiceInfo from './NextOrEqualLevelServiceInfo';
import styles from './index.less';

let countForConsole = 0;

const Mention: React.FC = () => {
  const dispatch = useDispatch();
  const [count, setCount] = useState(0);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [orderCalcResult, setOrderCalcResult] = useState<CalcResult>();
  const [showPayment, setShowPayment] = useState(false);
  const [timer, setTimer] = useState<NodeJS.Timer>();
  const [usingUpgrade, setUsingUpgrade] = useState(false);

  const deviceInfo: DeviceInfoWithPackage | undefined = useSelector(
    ({ device }: ConnectState) => device.deviceInfo,
  );

  const selectedProductSku: ProductSku | undefined = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku,
  );

  const gotoDescriptionPage = (type: DescriptionTypeEnum) => {
    // if (type === DescriptionTypeEnum.INTRODUCTION) {
    //   history.push('/entrance?hasNavBar=1');
    // } else {
    // }
    history.push(`/description/${type}?hasNavBar=1`);
  };

  const purchaseButtonProtocol = useMemo(() => {
    if (!selectedProductSku) return <></>;
    const desciptionProtocol = (
      <span
        onClick={() =>
          gotoDescriptionPage(DescriptionTypeEnum.DESCRIPTION_PROTOCOL)
        }
      >
        《PETKIT Care+ 服务说明协议》
      </span>
    );
    const autoRenewProtocol = (
      <span
        onClick={() =>
          gotoDescriptionPage(DescriptionTypeEnum.AUTO_RENEW_PROTOCOL)
        }
      >
        《自动续费服务协议》
      </span>
    );

    // 活动套餐，目前只可能是非自动续费套餐
    if (selectedProductSku.actPackage) {
      return <>{desciptionProtocol}</>;
    }
    // 非活动套餐 自动续费
    if (selectedProductSku.price.isReNew) {
      return (
        <>
          {desciptionProtocol}及{autoRenewProtocol}
        </>
      );
    }
    // 非活动套餐 非自动续费
    return <>{desciptionProtocol}</>;
  }, [selectedProductSku]);

  // 获取用户所选套餐的可支付方案
  const requestOrderCalc = async (
    _deviceInfo: DeviceInfoWithPackage,
    _selectedProductSku: ProductSku,
  ) => {
    try {
      const param: OrderCalcParam = {
        deviceId: _deviceInfo.deviceId,
        deviceType: _deviceInfo.deviceType,
        skuId: _selectedProductSku.id,
        // platform: PayTypeEnum.ALIPAY,
      };
      if (_selectedProductSku.actPackage) {
        param.activityId = _selectedProductSku.actPackage.actPackageId;
      }
      const result = await fetchOrderCalc(param);
      setOrderCalcResult(transferOrderCalcResultToCalcResult(result));
    } catch (error) {
      console.log(error);
      if (error && (error as any).message) {
        Toast.show((error as any).message);
        history.back();
        return;
      }
    } finally {
    }
  };

  // 获取用户所选套餐的可签约方案
  const requestSubscriptionOrderCalc = async (
    _deviceInfo: DeviceInfoWithPackage,
    _selectedProductSku: ProductSku,
  ) => {
    const param: SubscriptionOrderCalcParam = {
      deviceId: _deviceInfo.deviceId,
      deviceType: _deviceInfo.deviceType,
      skuId: _selectedProductSku.id,
    };
    // if (_selectedProductSku.actPackage) {
    //   param.activityId = _selectedProductSku.actPackage.actPackageId;
    // }
    const result = await fetchSubscriptionOrderCalc(param);
    setOrderCalcResult(transferSubscriptionCalcResultToCalcResult(result));
  };

  const requestReceive = async (isUpgrade: boolean) => {
    if (!deviceInfo) return;

    if (!selectedProductSku) {
      Toast.show({
        content: '当前没有可领取的免费套餐，请联系客服反馈',
      });
      return;
    }

    const param: OrderCreationV2Param = {
      deviceId: deviceInfo.deviceId,
      skuId: selectedProductSku.id,
      deviceType: deviceInfo.deviceType,
      platform: PayTypeEnum.EXPERIENCE,
      upgrade: +isUpgrade || NumberBooleanEnum.FALSE,
      activityId: selectedProductSku.actPackage?.actPackageId,
    };
    console.log('requestReceive', param);

    setButtonLoading(true);
    const toastRef = Toast.show({
      icon: 'loading',
      content: '领取中…',
    });

    setTimeout(async () => {
      try {
        const payResult = await fetchOrderCreationV2(param);
        toastRef.close();
        if (payResult && payResult.state === OrderStatusEnum.PAY_SUCCEED) {
          history.replace('/receive/result');
        }
      } catch (err: any) {
        Toast.show({
          content: err.message || '领取失败',
          icon: 'fail',
        });
      } finally {
        // 更新是否刷新首页商品列表的标识符
        dispatch({ type: 'utils/needProductListRefresh', payload: true });
        setButtonLoading(false);
      }
    }, 500);
  };

  // 同步签约结果
  const requestAsyncSubscriptionResult = async (_subNod: string) => {
    // 更新是否刷新首页商品列表的标识符
    dispatch({ type: 'utils/needProductListRefresh', payload: true });
    const result = await fetchSubscribeResult({ subNo: _subNod });
    setTimeout(() => {
      setTimer(undefined);
      clearInterval(timer);
      if (result.state === SubscriptionStateEnum.ACTIVITY) {
        // 跳转到签约成功页面
        history.replace(
          `/subscription/result?subNo=${_subNod}&state=${result.state}`,
        );
      } else if (result.state === SubscriptionStateEnum.SIGNING) {
        // 跳转到提醒到支付宝完成签约的界面
        history.replace(
          `/subscription/result?subNo=${_subNod}&state=${result.state}`,
        );
      } else {
        let statusName = '签约已解约~';
        if (result.state === SubscriptionStateEnum.CANCELLED)
          statusName = '签约已取消~';
        Dialog.clear();
        Toast.show(statusName);
      }
    }, 3000);
  };

  // 同步支付结果
  const requestAsyncPaymentResult = async (_orderId: string) => {
    // 更新是否刷新首页商品列表的标识符
    dispatch({ type: 'utils/needProductListRefresh', payload: true });
    const result = await fetchPaymentResult(_orderId);
    if (result.state === OrderStatusEnum.PAY_SUCCEED) {
      global.aplus.buryPoint(PointTypeEnum.BUYNOW_PAID_RESULT, {
        method: AplusPayResultTypeEnum.SUCCESS,
      });
      const toastRef = Toast.show({
        icon: 'loading',
        // content: '加载中…',
        duration: 0,
      });
      const _timer = setTimeout(() => {
        // 跳转到我的云服务
        global.gotoCurrentDeviceServicePage();
        clearTimeout(_timer);
        toastRef.close();
      }, 3000);
    } else if (result.state === OrderStatusEnum.PAYING) {
      history.replace(`/order/list`);
    }
    setTimer(undefined);
    clearInterval(timer);
  };

  // 展示是否完成支付的弹框
  const showPaymentResultDialog = (orderId: string) => {
    Dialog.clear();
    Dialog.confirm({
      // title: '确认',
      content: (
        <div
          style={{ textAlign: 'center' }}
          onClick={() => {
            countForConsole += 1;
            console.log(countForConsole);
            if (countForConsole >= 3) {
              global.setupVConsole();
            }
          }}
        >
          您是否已完成支付？
        </div>
      ),
      confirmText: (
        <span style={{ fontSize: 14, fontWeight: 'normal' }}>已完成支付</span>
      ),
      cancelText: (
        <span style={{ fontSize: 14, fontWeight: 'bold', color: '#999' }}>
          支付碰到问题
        </span>
      ),
      onCancel: () => {
        sessionStorage.removeItem('payResultInfo');
        requestAsyncPaymentResult(orderId);
      },
      onConfirm: () => {
        sessionStorage.removeItem('payResultInfo');
        requestAsyncPaymentResult(orderId);
      },
    });
  };

  // 展示是否完成签约的弹框
  const showSubscriptionResultDialog = (subNod: string) => {
    Dialog.clear();
    Dialog.confirm({
      // title: '确认',
      content: (
        <div
          style={{ textAlign: 'center' }}
          onClick={() => {
            countForConsole += 1;
            console.log(countForConsole);
            if (countForConsole >= 3) {
              global.setupVConsole();
            }
          }}
        >
          您是否已完成签约？
        </div>
      ),
      confirmText: (
        <span style={{ fontSize: 14, fontWeight: 'normal' }}>已完成</span>
      ),
      cancelText: (
        <span style={{ fontSize: 14, fontWeight: 'bold', color: '#999' }}>
          签约碰到问题
        </span>
      ),
      onCancel: () => {
        sessionStorage.removeItem('subscribeResultInfo');
        requestAsyncSubscriptionResult(subNod);
      },
      onConfirm: () => {
        sessionStorage.removeItem('subscribeResultInfo');
        requestAsyncSubscriptionResult(subNod);
      },
    });
  };

  // 关闭支付弹框
  const closeConfirm = () => {
    setShowPayment(false);
    setUsingUpgrade(false);
  };

  // 判断当前所选套餐是否存在未支付的情况
  const requestOrderStateCheck = async () => {
    const result = await fetchOrderStateCheck();
    return result;
  };

  // 去支付
  const goToPayment = async (isUpgrade: boolean) => {
    setButtonLoading(true);

    const orderStateCheckResult = await requestOrderStateCheck();
    if (
      orderStateCheckResult[OrderStatusEnum.PAYING] &&
      orderStateCheckResult[OrderStatusEnum.PAYING].length
    ) {
      Dialog.confirm({
        content: '您有待支付的订单，请先处理',
        confirmText: (
          <div style={{ color: '#3378F8', fontSize: 15, fontWeight: 400 }}>
            查看订单
          </div>
        ),
        cancelText: <div style={{ color: '#999', fontSize: 15 }}>取消</div>,
        onConfirm: () => {
          history.push(
            `/order/list?deviceId=${deviceInfo?.deviceId}&deviceType=${deviceInfo?.deviceType}`,
          );
        },
      });
      setButtonLoading(false);
      return;
    }

    try {
      if (
        (isUpgrade
          ? orderCalcResult?.upgradeAmount
          : orderCalcResult?.purchaseAmount) === 0
      ) {
        setButtonLoading(false);
        requestReceive(isUpgrade);
        return;
      }
      setShowPayment(true);
      setUsingUpgrade(isUpgrade);
      setButtonLoading(false);
    } catch (error) {
      console.log(error);
      setButtonLoading(false);
    }
  };

  useEffect(() => {
    if (!deviceInfo || !selectedProductSku) {
      history.back();
      return;
    }

    if (!selectedProductSku.price.isReNew || !!selectedProductSku.actPackage) {
      // 要购买的套餐为非自动续费套餐
      requestOrderCalc(deviceInfo, selectedProductSku);
      return;
    }
    requestSubscriptionOrderCalc(deviceInfo, selectedProductSku);
  }, [deviceInfo, selectedProductSku]);

  if (!deviceInfo || !selectedProductSku || !orderCalcResult) return <></>;

  return (
    <>
      <div
        className={`${
          styles.container
        } colorful-background h-full overflow-auto relative ${
          global.isiOS() ? 'padding-safe-area-ios' : ''
        } ${global.isAndroid() ? 'padding-safe-area-android' : ''} ${
          global.isHarmony() ? 'padding-safe-area-harmony' : ''
        }`}
      >
        <NavBar
          onBack={history.back}
          style={{ marginTop: 10 }}
          right={
            count >= 3 ? (
              <AntOutline fontSize={20} onClick={global.setupVConsole} />
            ) : null
          }
        >
          <span
            className="active-color font-bold text-lg"
            onClick={() => setCount(count + 1)}
          >
            开通提醒
          </span>
        </NavBar>

        <div className={styles.content}>
          <div className={styles.title}>当前套餐</div>
          <DeviceInfo
            className={styles.deviceInfo}
            deviceInfo={deviceInfo}
            showPendingPackageList
          />
          <CurrentServiceInfo
            deviceInfo={deviceInfo}
            selectedProductSku={selectedProductSku}
            orderCalcResult={orderCalcResult}
          />
          <NextOrEqualLevelServiceInfo
            deviceInfo={deviceInfo}
            selectedProductSku={selectedProductSku}
            orderCalcResult={orderCalcResult}
          />
          {/* 正常支付按钮 */}
          <br />
          {orderCalcResult.purchase && !orderCalcResult.upgrade ? (
            <section className="mb-7 flex items-center justify-center flex-col w-full">
              <button
                type="button"
                className={classNames('w-full', styles.purchaseButton, {
                  [styles.purchaseButtonActivity]:
                    !!selectedProductSku.actPackage,
                })}
                onClick={() => goToPayment(false)}
                disabled={buttonLoading}
              >
                {buttonLoading ? (
                  <>购买中...</>
                ) : (
                  <>
                    <div
                      className={classNames(styles.purchaseButtonTitle, {
                        [styles.purchaseButtonTitleActivity]:
                          !!selectedProductSku.actPackage,
                      })}
                    >
                      立即以
                      <PriceInfo
                        symbol={
                          orderCalcResult.purchaseProduct?.currency
                            ?.currencySymbol || global.DEFAULT_CURRENCY_SYMBOL
                        }
                        price={orderCalcResult.purchaseAmount || 0}
                      />
                      开通
                    </div>
                    {/* {selectedProductSku.actPackage ? (
                      <div
                        className={classNames(styles.purchaseButtonSubtitle, {
                          [styles.purchaseButtonSubtitleActivity]:
                            !!selectedProductSku.actPackage,
                        })}
                      >
                        活动时间：
                        {dayjs(selectedProductSku.actPackage.effectTime).format(
                          'YYYY-MM-DD',
                        )}{' '}
                        -{' '}
                        {dayjs(
                          selectedProductSku.actPackage.inEffectTime,
                        ).format('YYYY-MM-DD')}
                      </div>
                    ) : null} */}
                  </>
                )}
              </button>
              <p
                className="gray-text-color mt-3.5"
                style={{
                  marginLeft: 14,
                  marginRight: 14,
                  textAlign: 'center',
                  fontSize: 12,
                }}
              >
                开通前阅读{purchaseButtonProtocol}
              </p>
            </section>
          ) : null}
          {/* 签约支付按钮 */}
          {orderCalcResult.sign && !orderCalcResult.upgrade ? (
            <section className="mb-7 flex items-center justify-center flex-col w-full">
              <button
                type="button"
                className={classNames('w-full', styles.signButton, {
                  [styles.signButtonActivity]: !!selectedProductSku.actPackage,
                })}
                onClick={() => goToPayment(false)}
                disabled={buttonLoading}
              >
                <div
                  className={classNames(styles.signButtonTitle, {
                    [styles.signButtonTitleActivity]:
                      !!selectedProductSku.actPackage,
                  })}
                >
                  立即以
                  <PriceInfo
                    symbol={
                      orderCalcResult.signProduct?.currency?.currencySymbol ||
                      global.DEFAULT_CURRENCY_SYMBOL
                    }
                    price={orderCalcResult.signAmount || 0}
                  />
                  开通
                </div>
                <div
                  className={classNames(styles.signButtonSubtitle, {
                    [styles.signButtonSubtitleActivity]:
                      !!selectedProductSku.actPackage,
                  })}
                >
                  到期自动续费，可随时取消
                </div>
              </button>
              <p
                className="gray-text-color mt-3.5"
                style={{
                  marginLeft: 14,
                  marginRight: 14,
                  textAlign: 'center',
                  fontSize: 12,
                }}
              >
                开通前阅读{purchaseButtonProtocol}
              </p>
            </section>
          ) : null}
          {/* 选择开通方式流程 */}
          {(orderCalcResult.purchase || orderCalcResult.sign) &&
          orderCalcResult.upgrade ? (
            <ActivationWay
              selectedProductSku={selectedProductSku}
              orderCalcResult={orderCalcResult}
              buttonLoading={buttonLoading}
              goToPayment={goToPayment}
              deviceInfo={deviceInfo}
            />
          ) : null}
        </div>
      </div>
      <Popup
        visible={showPayment}
        destroyOnClose
        bodyClassName="rounded-t-2xl"
        showCloseButton
        bodyStyle={{
          height:
            !selectedProductSku?.price.isReNew &&
            selectedProductSku?.relationSkuId
              ? '370px'
              : '320px',
        }}
        onMaskClick={closeConfirm}
        onClose={closeConfirm}
      >
        <div className="p-5">
          <div className="flex justify-between items-center mb-8">
            {/* <div /> */}
            <h3 className="font-bold text-lg w-full text-center mb-0">
              订单结算
            </h3>
            {/* <CloseOutline
              className="text-lg font-bold"
              color="#111"
              onClick={closeConfirm}
            /> */}
          </div>
          {deviceInfo ? (
            <Confirm
              deviceId={deviceInfo.deviceId}
              deviceType={deviceInfo.deviceType}
              upgrade={usingUpgrade}
              orderCalcResult={orderCalcResult}
              showRelativeSku={
                (!deviceInfo.pendingProductInfos ||
                  !deviceInfo.pendingProductInfos.length) &&
                !usingUpgrade &&
                !selectedProductSku.actPackage
              }
              createSubscriptionSuccessFunction={(subNo: string) => {
                setShowPayment(false);
                showSubscriptionResultDialog(subNo);
              }}
              createPaySuccessFunction={(
                orderId: string,
                payType: PayTypeEnum,
              ) => {
                if (payType === PayTypeEnum.WEIXIN) {
                  setShowPayment(false);
                  setTimeout(() => {
                    if (global.isiOS()) showPaymentResultDialog(orderId);
                  }, 800);
                }
              }}
            />
          ) : (
            ''
          )}
        </div>
      </Popup>
    </>
  );
};

export default Mention;
