import PriceInfo from '@/components/PriceInfo';
import { DeviceInfoWithPackage } from '@/models/device/interface';
import { ProductSku } from '@/models/product/interface';
import { CalcResult } from '@/pages/interface';
import global from '@/utils/global';
import { Dialog } from 'antd-mobile';
import { QuestionCircleOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import dayjs from 'dayjs';
import styles from './index.less';

interface Props {
  deviceInfo: DeviceInfoWithPackage;
  orderCalcResult: CalcResult;
  selectedProductSku: ProductSku;
}

const CurrentServiceInfo: React.FC<Props> = ({
  deviceInfo,
  orderCalcResult,
  selectedProductSku,
}: Props) => {
  if (!deviceInfo.pendingProductInfos || !deviceInfo.pendingProductInfos.length)
    return (
      <>
        <div className={`${styles.productInfo}`}>
          当前设备已拥有“
          <span className={classNames('warning-color')}>
            {orderCalcResult.currentProduct.skuName}
          </span>
          ”套餐（
          <span className={classNames('warning-color')}>
            有效期
            {dayjs(orderCalcResult.currentProduct.workTime)
              .tz(deviceInfo.zoneId)
              .format('YYYY/MM/DD')}
            ~
            {dayjs(orderCalcResult.currentProduct.workInDate)
              .tz(deviceInfo.zoneId)
              .format('YYYY/MM/DD')}
          </span>
          ）。
        </div>
        {orderCalcResult.currentProduct?.remainAmount ? (
          <div className="flex items-center">
            当前套餐剩余价格
            <PriceInfo
              symbol={
                orderCalcResult.currentProduct?.currency?.currencySymbol ||
                global.DEFAULT_CURRENCY_SYMBOL
              }
              price={orderCalcResult.currentProduct?.remainAmount || 0}
            />
            <QuestionCircleOutline
              className={classNames(
                `${styles.payCardSubtitleIcon} text-[#999999]`,
              )}
              onClick={() =>
                Dialog.alert({
                  title: '当前套餐剩余价格说明',
                  closeOnMaskClick: true,
                  content:
                    '当前套餐剩余价格 = 套餐价格 - 套餐单日价格 * 套餐剩余使用时长',
                })
              }
            />
          </div>
        ) : null}
      </>
    );

  // 存在pending服务
  return (
    <div className={styles.productInfo}>
      当前设备已拥有“
      <span className={classNames('warning-color')}>多个套餐</span>
      ”，（确定要开通“
      <span className={classNames('warning-color')}>
        {selectedProductSku.actPackage?.actSkuName ||
          orderCalcResult.purchaseProduct?.activity?.actName ||
          orderCalcResult.purchaseProduct?.skuName ||
          orderCalcResult.signProduct?.activity?.actName ||
          orderCalcResult.signProduct?.skuName}
      </span>
      ”套餐）。
    </div>
  );
};

export default CurrentServiceInfo;
