import { fetchPaymentResult } from '@/models/order/fetch';
import { OrderStatusEnum } from '@/models/order/interface';
import { orderService } from '@/models/order/service';
import global from '@/utils/global';
import useUrlState from '@ahooksjs/use-url-state';
import { history, useDispatch } from '@umijs/max';
import { Button, NavBar } from 'antd-mobile';
import { CheckCircleFill, ExclamationCircleFill } from 'antd-mobile-icons';
import { useEffect } from 'react';

interface UrlQueryParam {
  orderId: string;
  state: OrderStatusEnum;
  errCode: string;
  errMsg: string;
}

const initUrlQueryParam: UrlQueryParam = {
  orderId: '',
  state: OrderStatusEnum.NONE,
  errCode: '0',
  errMsg: '',
};

const PaymentResult = () => {
  const dispatch = useDispatch();
  const [urlParam, setUrlParam] = useUrlState<UrlQueryParam>(initUrlQueryParam);

  useEffect(() => {
    global.aplus.sendPV();
    dispatch({
      type: 'product/selectProductSkuById',
      payload: 0,
    });
  }, []);

  const back = () => {
    history.back();
  };

  const continuePay = async (orderId: string) => {
    // 先同步订单支付结果
    const asyncOrder = await fetchPaymentResult(orderId);

    if (asyncOrder.state === OrderStatusEnum.PAYING) {
      // 如果结果确实还是支付中状态，发起重新支付的情况
      const url = await orderService.payAgain(asyncOrder.platform, orderId);
      if (url) window.open(url, '_self');
      return;
    }
    // 如果结果为其它状态 提示状态结果
    // 同步更新当前订单状态

    if (asyncOrder.state === OrderStatusEnum.PAY_FAILED) {
      setUrlParam({ ...urlParam, state: OrderStatusEnum.PAY_FAILED });
      return;
    }

    if (asyncOrder.state === OrderStatusEnum.PAY_SUCCEED) {
      setUrlParam({
        orderId: urlParam.orderId,
        state: OrderStatusEnum.PAY_SUCCEED,
      });
    }
  };

  return (
    <div
      className={`h-full relative ${
        global.isiOS() ? 'padding-safe-area-ios' : ''
      } ${global.isAndroid() ? 'padding-safe-area-android' : ''} ${
        global.isHarmony() ? 'padding-safe-area-harmony' : ''
      }`}
    >
      <NavBar onBack={back} className="mb-24">
        <span className="active-color font-bold text-lg">订单结算</span>
      </NavBar>
      {urlParam.state === OrderStatusEnum.PAY_SUCCEED ? (
        <>
          <div className="flex items-center flex-col">
            <CheckCircleFill
              className="text-7xl"
              style={{ color: '#6cb636' }}
            />
            <p className="mt-5 text-xl active-color">支付成功</p>
          </div>
          <div className="w-auto absolute left-4 right-5 bottom-14">
            <Button
              block
              shape="rounded"
              color="primary"
              style={{ backgroundColor: 'var(--color)' }}
              onClick={global.gotoMyCloudServiceList}
            >
              查看我的云服务
            </Button>
          </div>
        </>
      ) : (
        ''
      )}
      {urlParam.state !== OrderStatusEnum.PAY_SUCCEED ? (
        <div className="flex flex-col pl-5 pr-5">
          <div className="flex items-center flex-col">
            <ExclamationCircleFill
              className="text-7xl"
              style={{ color: '#e3762d' }}
            />
            <p className="mt-5 text-xl active-color">支付失败</p>
          </div>
          {+urlParam.errCode !== 0 && urlParam.errMsg ? (
            <div className="mt-12">
              <h6 className="active-color text-base font-500">失败原因：</h6>
              <p className="text-sm mt-1">
                {(urlParam.errMsg as string) || '请检查网络是否正常'}
              </p>
            </div>
          ) : (
            ''
          )}
          <div className="absolute left-4 right-4 bottom-10 flex flex-col justify-center">
            <a
              className="mb-5 text-center"
              style={{ color: '#1677ff' }}
              onClick={global.gotoCustomerService}
            >
              联系客服
            </a>
            <Button
              block
              shape="rounded"
              color="primary"
              className="w-full"
              style={{ backgroundColor: 'var(--color)' }}
              onClick={() => continuePay(urlParam.orderId)}
            >
              继续支付
            </Button>
          </div>
        </div>
      ) : (
        ''
      )}
    </div>
  );
};

export default PaymentResult;
