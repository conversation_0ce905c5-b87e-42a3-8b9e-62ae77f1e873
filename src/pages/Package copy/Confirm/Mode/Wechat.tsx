import { CheckOutline } from 'antd-mobile-icons';
import React, { useEffect } from 'react';

interface Props {
  isSelected: boolean;
  isPaying: boolean;
}

const WechatPayment: React.FC<Props> = ({ isSelected, isPaying }: Props) => {
  useEffect(() => {
    if (!isPaying) return;

    console.log('微信开始支付');
  }, [isPaying]);
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <i
          className="petkit-cloud-font petkit-cloud-weixinzhifu text-2xl mr-3"
          style={{ color: '#6bc839' }}
        />{' '}
        微信支付
      </div>
      {isSelected ? <CheckOutline className="text-2xl" style={{ color: '#3378f8' }} /> : ''}
    </div>
  );
};

export default WechatPayment;
