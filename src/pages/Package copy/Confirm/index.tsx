import PayComp from '@/components/PayComp';
import { ConnectState } from '@/models/connect';
import { DeviceInfoWithPackage } from '@/models/device/interface';
import { fetchSubscriptionSignV2 } from '@/models/order/fetch';
import {
  OrderCreationParam,
  PayTypeEnum,
  SubscriptionResult,
} from '@/models/order/interface';
import { fetchProductDetail } from '@/models/product/fetch';
import {
  ProductSku,
  ProductSkuDetailParam,
  SuitableDeviceTypeEnum,
} from '@/models/product/interface';
import { productService } from '@/models/product/service';
import { CalcResult } from '@/pages/interface';
import { getValidDataMessage } from '@/pages/util';
import { AplusCheckoutConfirmPayTypeEnum, PointTypeEnum } from '@/utils/aplus';
import global from '@/utils/global';
import { ErrorInfo } from '@mantas/request';
import { useNavigate, useSelector } from '@umijs/max';
import { <PERSON><PERSON>, Card, Checkbox, Dialog } from 'antd-mobile';
import { Toast } from 'antd-mobile/es';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PackageInfo from './PackageInfo';

import PriceInfo from '@/components/PriceInfo';
import './index.less';
import { getConfirmPackageSkuInfo } from './util';
// import global from '@/utils/global';
// import { AplusCheckoutConfirmPayTypeEnum, PointTypeEnum } from '@/utils/aplus';

interface Props {
  deviceId: number;
  deviceType: SuitableDeviceTypeEnum;
  upgrade?: boolean;
  orderCalcResult?: CalcResult;
  showRelativeSku?: boolean;
  createSubscriptionSuccessFunction?: (subNo: string) => void;
  createPaySuccessFunction?: (orderId: string, payType: PayTypeEnum) => void;
}

const Confirm: React.FC<Props> = ({
  deviceId,
  deviceType,
  upgrade = false,
  orderCalcResult,
  showRelativeSku = true,
  createPaySuccessFunction,
  createSubscriptionSuccessFunction,
}: Props) => {
  // const [weixinUrl, setWeixinUrl] = useState('');
  const [isSubscribe, setIsSubscribe] = useState(false);
  const [isSubscribing, setIsSubscribing] = useState(false);
  const navigate = useNavigate();
  const [productSku, setProductSku] = useState<ProductSku>();
  const [subscriptionProductSku, setSubscriptionProductSku] =
    useState<ProductSku>();
  const selectedProductSku: ProductSku | undefined = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku,
  );
  const deviceInfo: DeviceInfoWithPackage | undefined = useSelector(
    ({ device }: ConnectState) => device.deviceInfo,
  );
  // const aRef = useRef<HTMLAnchorElement>(null);

  const confirmPackageSkuInfo = useMemo(() => {
    return getConfirmPackageSkuInfo(
      selectedProductSku,
      isSubscribe,
      subscriptionProductSku,
    );
  }, [selectedProductSku, isSubscribe, subscriptionProductSku]);

  const linePrice = useMemo(() => {
    if (upgrade) {
      return productService.getPaymentPrice(deviceInfo, productSku);
    }

    return confirmPackageSkuInfo.linePrice;
  }, [upgrade, deviceInfo, confirmPackageSkuInfo, productSku]);

  const payPrice = useMemo(() => {
    if (upgrade && orderCalcResult && orderCalcResult.upgradeAmount) {
      return orderCalcResult.upgradeAmount;
    }
    // if (confirmPackageSkuInfo.)
    return confirmPackageSkuInfo.price;
  }, [upgrade, orderCalcResult, confirmPackageSkuInfo]);

  const showPayComp = useMemo(() => {
    if (isSubscribe) return false;

    if (!!selectedProductSku?.actPackage) return true;

    if (!selectedProductSku?.price.isReNew) return true;
  }, [isSubscribe, selectedProductSku]);

  // useEffect(() => {
  //   if (!weixinUrl || !aRef.current) return;

  //   console.log(aRef.current);
  //   aRef.current.click();
  // }, [aRef, weixinUrl]);

  // useEffect(() => {
  //   console.log('expirationDateRange', expirationDateRange);
  // }, [expirationDateRange]);

  const onAutoPayCheckBoxChanged = (ev: boolean) => {
    setIsSubscribe(ev);
  };

  const requestProductSkuDetail = async (
    skuId: number,
    deviceType: SuitableDeviceTypeEnum,
    deviceId: number,
  ) => {
    const productSkuDetail = await fetchProductDetail({
      skuId,
      deviceType,
      deviceId,
    } as ProductSkuDetailParam);
    setSubscriptionProductSku(productSkuDetail);
  };

  const onBeforeCreatePayFunction = useCallback(
    (_selectedProductSku?: ProductSku) => {
      if (!_selectedProductSku) return false;
      if (
        deviceInfo
        // &&
        // deviceInfo.effectiveProductInfo &&
        // deviceInfo.effectiveProductInfo.subscribe
      ) {
        const message = getValidDataMessage(deviceInfo, _selectedProductSku);
        // console.log(message, deviceInfo, _selectedProductSku);
        if (message) {
          Dialog.alert({
            title: '温馨提示',
            content: message,
          });
        }
        // Toast.show({ content: message });
        return !message;
      }
      return true;
    },
    [deviceInfo],
  );

  const confirmToSubscribe = useCallback(
    async (paymentMode: PayTypeEnum) => {
      if (!productSku) return;

      if (!onBeforeCreatePayFunction(productSku)) {
        return;
      }

      const param: OrderCreationParam = {
        deviceId: +(deviceId || 0),
        skuId: productSku.id,
        deviceType:
          (deviceType as SuitableDeviceTypeEnum) || SuitableDeviceTypeEnum.D4h,
        platform: paymentMode,
        upgrade: +upgrade,
      };
      let subscribeResult: SubscriptionResult;
      setIsSubscribing(true);

      // 支付宝订阅
      if (isSubscribe) {
        global.aplus.buryPoint(PointTypeEnum.BUYNOW_CHECKOUT_CONFIRM, {
          method: AplusCheckoutConfirmPayTypeEnum.ALIPAY_AUTO_RENEW,
        });
        try {
          subscribeResult = await fetchSubscriptionSignV2(param);
          sessionStorage.setItem(
            'subscribeResultInfo',
            JSON.stringify({
              ...subscribeResult,
              deviceId: +(deviceId || 0),
              deviceType,
            }),
          );
          if (createSubscriptionSuccessFunction)
            createSubscriptionSuccessFunction(subscribeResult.subNo);
          window.open(subscribeResult.payload, '_self');
        } catch (e: ErrorInfo) {
          console.log(e);
          if (typeof e === 'object' && e.message) {
            Toast.show({
              content: e.message,
              duration: (window as any).toastDuration || 2000,
            });
          }
        } finally {
          setIsSubscribing(false);
        }
      }
    },
    [isSubscribe, productSku],
  );

  useEffect(() => {
    if (!selectedProductSku) {
      navigate(-1);
      return;
    }
    setProductSku(selectedProductSku);
    if (selectedProductSku.relationSkuId)
      requestProductSkuDetail(
        selectedProductSku.relationSkuId,
        deviceType,
        deviceId,
      );

    // 选中商品身上本身的isReNew属性为True，同时该商品也并不是活动服务
    setIsSubscribe(
      selectedProductSku.price.isReNew && !selectedProductSku.actPackage,
    );
  }, [selectedProductSku, navigate]);

  useEffect(() => {
    if (isSubscribe) {
      setProductSku(subscriptionProductSku || selectedProductSku);
    } else {
      setProductSku(selectedProductSku);
    }
  }, [isSubscribe, subscriptionProductSku, selectedProductSku]);

  return (
    <div
      className={`payment-container h-full ${
        global.isiOS() ? 'padding-bottom-safe-area-ios' : ''
      } ${global.isAndroid() ? 'padding-bottom-safe-area-android' : ''} ${
        global.isHarmony() ? 'padding-bottom-safe-area-harmony' : ''
      }`}
    >
      <Card
        className=""
        headerClassName="p-0"
        bodyClassName="pb-0 pt-4"
        title={
          <PackageInfo
            deviceInfo={deviceInfo}
            confirmPackageSkuInfo={confirmPackageSkuInfo}
            isSubscribe={isSubscribe}
            selectedProductSku={selectedProductSku}
            upgrade={upgrade}
            orderCalcResult={orderCalcResult}
            subscriptionProductSku={subscriptionProductSku}
          />
        }
      >
        <div>
          {showRelativeSku &&
          selectedProductSku &&
          (!selectedProductSku?.price.isReNew ||
            !!selectedProductSku.actPackage) &&
          selectedProductSku.relationSkuId &&
          subscriptionProductSku &&
          subscriptionProductSku.saleStatus ? (
            <Checkbox
              className="auto-pay-checkbox mb-4"
              checked={isSubscribe}
              onChange={onAutoPayCheckBoxChanged}
            >
              下月自动续费，可随时关闭
            </Checkbox>
          ) : (
            ''
          )}

          <div className="mb-4">
            <div className="flex justify-between items-center">
              <p className="default-color">支付金额</p>
              <div>
                <span className="line-through default-color text-xs">
                  {linePrice ? (
                    <PriceInfo
                      symbol={
                        confirmPackageSkuInfo.priceCurrencySymbol ||
                        global.DEFAULT_CURRENCY_SYMBOL
                      }
                      price={linePrice}
                    />
                  ) : null}
                </span>
                <span className="active-color text-1.5xl">
                  <PriceInfo
                    symbol={
                      confirmPackageSkuInfo.priceCurrencySymbol ||
                      global.DEFAULT_CURRENCY_SYMBOL
                    }
                    price={payPrice}
                  />
                </span>
              </div>
            </div>
            {/* {productSku?.price.isReNew ? (
              <div>
                次年按￥{productSku?.price.price}/{productSku?.serviceTime}
                {productSku && productSku.serviceTimeUnit
                  ? serviceTimeUnitNames[productSku?.serviceTimeUnit]
                  : ''}
                自动续费，可随时关闭
              </div>
            ) : (
              ''
            )} */}
          </div>
        </div>
      </Card>
      {showPayComp ? (
        <PayComp
          deviceId={deviceInfo?.deviceId}
          deviceType={deviceInfo?.deviceType}
          productSku={productSku}
          payTimes="first"
          beforeCreatePayFunction={onBeforeCreatePayFunction}
          createPaySuccessFunction={createPaySuccessFunction}
          upgrade={upgrade}
        />
      ) : (
        <div className="flex justify-center items-center flex-1">
          <Button
            className={`flex-grow`}
            shape="rounded"
            style={{ backgroundColor: '#027aff', color: '#fff' }}
            onClick={() => confirmToSubscribe(PayTypeEnum.ALIPAY)}
            loading={isSubscribing}
          >
            支付宝签约
          </Button>
        </div>
      )}
    </div>
  );
};

export default Confirm;
