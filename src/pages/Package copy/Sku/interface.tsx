import { Key } from '@/models/common.interface';
import {
  Benefit,
  Currency,
  ProductSkuActivityInfo,
  ProductSkuCapacityTypeEnum,
  RecordTypeEnum,
  ServiceDurationEnumType,
} from '@/models/product/interface';

export interface OrderDuration extends Key {
  type: ServiceDurationEnumType;
  name: string;
  packageSkuList: PackageSkuInfo[];
}

export interface PackageSkuInfo {
  id: number;
  name: string;
  currency: Currency;
  price: number;
  linePrice: number;
  // pricePerDay: number;
  pricePerUnit: {
    serviceTimeUnit: string;
    price: number;
    symbol: string;
  };
  iconUrl: string;
  /**
   * @deprecated
   */
  capacities: Array<{
    type: ProductSkuCapacityTypeEnum;
    recordType: RecordTypeEnum;
  }>;
  /**
   * @deprecated
   */
  cycleTime: number;
  isRenew: boolean;
  firstPrice: number;
  relationSkuId: number;
  description: string;
  benefits: Benefit[];
  cornerMarkIcon: string;
  serviceTimeUnit: ServiceDurationEnumType;
  actPackage?: ProductSkuActivityInfo;
}
