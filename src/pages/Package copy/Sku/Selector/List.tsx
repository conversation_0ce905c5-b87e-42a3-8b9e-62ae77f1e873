import noDataImage from '@/assets/order-empty.png';
import InfoPopup from '@/components/InfoPopup';
import { ConnectState } from '@/models/connect';
import {
  ProductSku,
  ServiceDurationEnumType,
} from '@/models/product/interface';
import type { Dispatch } from '@umijs/max';
import { useDispatch, useSelector } from '@umijs/max';
import { ErrorBlock } from 'antd-mobile';
import React, { useEffect, useRef, useState } from 'react';
import { PackageSkuInfo } from '../interface';
import { skuService } from '../service';
import SkuSelectorItem from './Item';

interface Props {
  currentTab: ServiceDurationEnumType;
  selectedTabKey: ServiceDurationEnumType;
  packageSkuList?: PackageSkuInfo[];
}

interface PackageInfo {
  title: string;
  content: string;
  imageUrl: string;
}

const SkuSelectorList: React.FC<Props> = ({
  currentTab,
  selectedTabKey,
  packageSkuList = [],
}: Props) => {
  const dispatch: Dispatch = useDispatch();
  const [selectedPackageInfo, setSelectedPackageInfo] = useState<PackageInfo>();
  const [showPackageInfo, setShowPackageInfo] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [scrollDistance, setScrollDistance] = useState(
    skuService.tabPaneScrollLeft,
  );

  const selectedProductSku: ProductSku = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku,
  );

  // const packageInfoObj: {
  //   [key in SkuTypeEnum]: PackageInfo;
  // } = {
  //   [SkuTypeEnum.EVENT]: {
  //     title: '事件录制',
  //     content: '摄像机捕捉到事件时录制',
  //     imageUrl: eventImg,
  //   },
  //   [SkuTypeEnum.CVR]: {
  //     title: '持续录制',
  //     content: '全天二十四小时持续录制',
  //     imageUrl: cvrImg,
  //   },
  //   [SkuTypeEnum.BASIC]: {
  //     title: '基础服务',
  //     content: '动态剪辑让视频变得更有趣',
  //     imageUrl: basicImg,
  //   },
  // };

  useEffect(() => {
    if (!dispatch || !packageSkuList || !packageSkuList.length) return;
    if (selectedTabKey && currentTab && currentTab === selectedTabKey) {
      dispatch({
        type: 'product/selectProductSkuById',
        payload: skuService.selectedProductSkuId || packageSkuList[0].id,
      });
    }
  }, [dispatch, selectedTabKey, currentTab, packageSkuList]);

  useEffect(() => {
    if (!scrollRef.current) return;

    scrollRef.current.scrollTo({ left: skuService.tabPaneScrollLeft });
  }, [scrollRef]);

  // const toShowPackageInfo = () => {
  //   setSelectedPackageInfo(packageInfoObj[selectedType]);
  //   setShowPackageInfo(true);
  // };

  const onInfoPopupClose = () => {
    setShowPackageInfo(false);
    setSelectedPackageInfo(undefined);
  };

  const getNoData = () => {
    return (
      <ErrorBlock
        image={noDataImage}
        style={{
          margin: '50px auto',
          textAlign: 'center',
          '--image-height': '146px',
        }}
        title={<p style={{ marginTop: '-40px', marginBottom: 0 }}>暂无数据</p>}
        description={null}
      />
    );
  };

  useEffect(() => {
    skuService.tabPaneScrollLeft = scrollDistance;
  }, [selectedProductSku]);

  return (
    <>
      <section>
        {/* {capacitiesNumber > 1 ? (
          <div className="flex items-center text-sm">
            {packageInfoObj[selectedType].content}{' '}
            <QuestionCircleOutline
              className="ml-2 default-color text-base cursor-pointer"
              onClick={toShowPackageInfo}
            />
          </div>
        ) : (
          ''
        )} */}
        {packageSkuList && packageSkuList.length ? (
          <div
            className="flex overflow-auto no-scroll-bar pt-5.5"
            ref={scrollRef}
            onScroll={(ev) => {
              setScrollDistance(ev.target.scrollLeft);
            }}
          >
            {packageSkuList.map((item) => (
              <SkuSelectorItem
                key={item.id}
                packageSkuInfo={item}
                onSelected={(scrollLeft) => {
                  setScrollDistance(scrollLeft);
                }}
              />
            ))}
          </div>
        ) : (
          getNoData()
        )}
      </section>
      {selectedPackageInfo ? (
        <InfoPopup
          visible={showPackageInfo}
          title={selectedPackageInfo?.title || ''}
          content={selectedPackageInfo?.content || ''}
          imageUrl={selectedPackageInfo?.imageUrl || ''}
          onClose={onInfoPopupClose}
        />
      ) : (
        ''
      )}
    </>
  );
};
export default SkuSelectorList;
