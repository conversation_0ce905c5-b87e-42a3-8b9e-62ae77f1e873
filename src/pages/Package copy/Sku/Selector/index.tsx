import { ServiceDurationEnumType } from '@/models/product/interface';
import { CapsuleTabs } from 'antd-mobile';
import React, { useState } from 'react';
import { OrderDuration } from '../interface';
import { skuService } from '../service';
import SkuSelectorList from './List';

interface Props {
  orderDurationList: OrderDuration[];
}

const Selector: React.FC<Props> = ({ orderDurationList = [] }: Props) => {
  const [activeKey, setActiveKey] = useState<ServiceDurationEnumType>(
    skuService.selectedTabs[0],
  );

  if (orderDurationList.length === 1) {
    return (
      <SkuSelectorList
        currentTab={orderDurationList[0].type}
        selectedTabKey={activeKey}
        packageSkuList={orderDurationList[0].packageSkuList || []}
      />
    );
  }

  return (
    <CapsuleTabs
      activeKey={activeKey}
      onChange={(key: ServiceDurationEnumType) => {
        console.log(key);
        // 缓存所选的tab值
        skuService.setSelectedTabs(key);
        setActiveKey(key);
        // 重置tabPane的滚动距离
        skuService.tabPaneScrollLeft = 0;
        // 重置所选择的SKU的id
        skuService.selectedProductSkuId = 0;
      }}
    >
      {orderDurationList.map((orderDuration) => (
        <CapsuleTabs.Tab
          title={orderDuration.name}
          key={orderDuration.type}
          destroyOnClose
        >
          <SkuSelectorList
            currentTab={orderDuration.type}
            selectedTabKey={activeKey}
            packageSkuList={orderDuration.packageSkuList || []}
          />
        </CapsuleTabs.Tab>
      ))}
    </CapsuleTabs>
  );
};
export default Selector;
