import {
  ProductSku,
  ServiceDurationEnum,
  ServiceDurationEnumType,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';
import { productService } from '@/models/product/service';
import { uuid } from '@/utils/uuid';
import { OrderDuration, PackageSkuInfo } from './interface';

// const serviceTimeUnitDays: { [key in ServiceDurationEnumType]: number } = {
//   [ServiceDurationEnum.YEAR]: 365,
//   [ServiceDurationEnum.MONTH]: 365 / 12,
//   [ServiceDurationEnum.DAY]: 1,
//   [ServiceDurationEnum.SEASON]: 365 / 3,
// };

export const serviceTimeUnitNames: {
  [key in ServiceDurationEnumType]: string;
} = {
  [ServiceDurationEnum.YEAR]: '年',
  [ServiceDurationEnum.MONTH]: '月',
  [ServiceDurationEnum.DAY]: '日',
  [ServiceDurationEnum.SEASON]: '季',
};

export const serviceDurationNameObj: {
  [key in ServiceDurationEnumType]: string;
} = {
  [ServiceDurationEnum.YEAR]: '包年',
  [ServiceDurationEnum.MONTH]: '包月',
  [ServiceDurationEnum.DAY]: '包天',
  [ServiceDurationEnum.SEASON]: '包季',
};

// const oneDayLoopBenefit: Benefit = {
//   id: 1,
//   name: '完整视频1天循环',
//   icon: oneDayLoop,
//   description: '完整视频1天循环',
//   image: '',
// };
// const sevenDaysLoopBenefit: Benefit = {
//   id: 2,
//   name: '完整视频7天循环',
//   icon: sevenDaysLoop,
//   description: '完整视频7天循环',
//   image: '',
// };
// const benefits: Benefit[] = [
//   {
//     id: 3,
//     name: '动态视频7天循环',
//     icon: sevenDaysLoop,
//     description: '动态视频7天循环',
//     image: '',
//   },
//   {
//     id: 4,
//     name: '云端高清录像',
//     icon: fullDisplay,
//     description: '云端高清录像',
//     image: '',
//   },
// ];

const transferProductSkuToPackageSkuInfo = (
  productSku: ProductSku,
  serviceTimeUnit: ServiceDurationEnumType,
): PackageSkuInfo => {
  const skuInfo: PackageSkuInfo = {
    id: productSku.id,
    name: productSku.shortName,
    price: productSku.price.price,
    linePrice: productSku.price.linePrice,
    // pricePerDay: +(
    //   productSku.price.price / serviceTimeUnitDays[productSku.serviceTimeUnit]
    // ),
    pricePerUnit: {
      serviceTimeUnit: '天',
      symbol: productSku.price.currency?.currencySymbol || '$',
      price: 0,
    },
    iconUrl: productSku.cornerMarkIcon,
    capacities: productSku.capacities,
    cycleTime: productSku.cycleTime,
    isRenew: productSku.price.isReNew,
    firstPrice: productSku.price.firstPhasePrice || 0,
    relationSkuId: productSku.relationSkuId,
    description: productSku.description,
    cornerMarkIcon: productSku.cornerMarkIcon,
    serviceTimeUnit,
    benefits: productSku.benefits,
    actPackage: productSku.actPackage,
    currency: productSku.price.currency,
  };

  const MONTHS_PER_YEAR = 12;
  const DAYS_PER_MONTH = 30;
  const skuServiceTimeUnit =
    productSku.actPackage?.serviceTimeUnit || productSku.serviceTimeUnit;
  const servicePrice =
    productSku.actPackage?.price.amount || productSku.price.price;

  switch (skuServiceTimeUnit) {
    case ServiceTimeUnitEnum.YEAR:
      skuInfo.pricePerUnit.serviceTimeUnit =
        serviceTimeUnitNames[ServiceTimeUnitEnum.MONTH];
      skuInfo.pricePerUnit.price = +(servicePrice / MONTHS_PER_YEAR).toFixed(2);
      break;
    case ServiceTimeUnitEnum.MONTH:
      skuInfo.pricePerUnit.serviceTimeUnit = '天';
      skuInfo.pricePerUnit.price = +(servicePrice / DAYS_PER_MONTH).toFixed(2);
      break;
    default:
      skuInfo.pricePerUnit.serviceTimeUnit = '天';
      skuInfo.pricePerUnit.price = +servicePrice.toFixed(2);
      break;
  }

  return skuInfo;
};

export const transferProductSkuListToOrderDurationList = (
  packageSkuList: ProductSku[],
): OrderDuration[] => {
  // 定义一个订单持续时间列表
  const orderDurationList: OrderDuration[] = [];
  // 定义一个服务时间产品sku对象
  const serviceDurationProductSkuObj: {
    [ServiceDurationEnum.YEAR]: ProductSku[];
    [ServiceDurationEnum.MONTH]: ProductSku[];
  } = {
    [ServiceDurationEnum.MONTH]: [],
    [ServiceDurationEnum.YEAR]: [],
    // [ServiceDurationEnum.SEASON]: [],
    // [ServiceDurationEnum.DAY]: [],
  };

  // serviceDurationProductSkuObj[ServiceDurationEnum.MONTH] = [...packageSkuList];

  for (let i = 0; i < packageSkuList.length; i++) {
    const sku = packageSkuList[i];
    if (!sku) continue;

    // if (sku.chargeType === ChargeTypeEnum.FREE) {
    //   serviceDurationProductSkuObj[ServiceDurationEnum.MONTH].push(sku);
    //   continue;
    // }

    const serviceDuration = productService.getSkuServiceTimeUnit(
      sku.serviceTime,
      sku.serviceTimeUnit,
    );

    if (
      !serviceDuration ||
      !serviceDurationProductSkuObj ||
      serviceDuration === ServiceDurationEnum.DAY ||
      serviceDuration === ServiceDurationEnum.SEASON ||
      !serviceDurationProductSkuObj[serviceDuration]
    ) {
      continue;
    }

    serviceDurationProductSkuObj[serviceDuration].push(sku);
  }

  for (const key in serviceDurationProductSkuObj) {
    if (serviceDurationProductSkuObj[key as 'YEAR' | 'MONTH']) {
      const _packageSkuList: ProductSku[] =
        serviceDurationProductSkuObj[key as 'YEAR' | 'MONTH'] || [];
      // if (!_packageSkuList ||!_packageSkuList.length) continue;

      // console.log('transferProductSkuListToOrderDurationList', _packageSkuList);
      const orderDuration: OrderDuration = {
        key: uuid(),
        type: key as ServiceDurationEnumType,
        name: serviceDurationNameObj[key as ServiceDurationEnumType],
        packageSkuList: _packageSkuList.map((sku) =>
          transferProductSkuToPackageSkuInfo(
            sku,
            key as ServiceDurationEnumType,
          ),
        ),
      };
      orderDurationList.push(orderDuration);
    }
  }

  // 将orderDurationList中的packageSkuList为空数组的orderDuration过滤掉
  const _orderDurationList = orderDurationList.filter(
    (orderDuration) => orderDuration.packageSkuList.length,
  );
  return _orderDurationList;
};
