import { Image } from 'antd-mobile';
import classNames from 'classnames';
import React from 'react';

interface Props {
  isLatest?: boolean;
  url: string;
  title: string;
  imageClassName?: string;
  titleClassName?: string;
  imageSize?: number;
  layout?: 'horizontal' | 'vertical';
}

const SkuBenefitItem: React.FC<Props> = ({
  isLatest = false,
  url,
  title,
  imageClassName = '',
  titleClassName = '',
  imageSize = 20,
  layout = 'horizontal',
}: Props) => {
  return (
    <div
      className={classNames(
        'w-full',
        'flex',
        { 'justify-content-center': layout === 'horizontal' },
        { 'items-center flex-col': layout === 'vertical' },
        { 'mb-0': isLatest },
        { 'mb-3.5': !isLatest && layout === 'horizontal' },
      )}
    >
      <div
        className={classNames(
          { 'w-5 h-5': layout === 'horizontal' },
          { 'mr-1.5': layout === 'horizontal' },
          { 'mb-2': layout === 'vertical' },
          imageClassName,
          'flex',
          'flex-grow-0',
          'flex-shrink-0',
        )}
      >
        <Image src={url} width={imageSize} height={imageSize} />
      </div>
      <p
        className={classNames(
          `text-xs normal-color ${titleClassName} mb-0 flex-wrap whitespace-normal`,
          { 'flex-shrink flex-grow-0': layout === 'horizontal' },
          { 'text-center w-full': layout === 'vertical' },
        )}
        style={{ minWidth: 50 }}
      >
        {title}
      </p>
    </div>
  );
};

export default SkuBenefitItem;
