import {
  ServiceDurationEnum,
  ServiceDurationEnumType,
} from '@/models/product/interface';

interface SkuService {
  DEFAULT_ACTIVE_KEY: ServiceDurationEnumType;
  // 当前所选择的tab值数组
  selectedTabs: ServiceDurationEnumType[];
  // tabPane中的滚动条位置
  tabPaneScrollLeft: number;
  selectedProductSkuId: number;

  // 存储所选择的tab值
  setSelectedTabs: (value: ServiceDurationEnumType) => void;
}

const DEFAULT_ACTIVE_KEY = ServiceDurationEnum.MONTH;

export const skuService: SkuService = {
  DEFAULT_ACTIVE_KEY,

  selectedTabs: [DEFAULT_ACTIVE_KEY],
  tabPaneScrollLeft: 0,
  selectedProductSkuId: 0,

  setSelectedTabs: (value: ServiceDurationEnumType) => {
    skuService.selectedTabs[0] = value;
  },
};
