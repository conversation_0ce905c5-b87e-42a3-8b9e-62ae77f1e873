import { SubscriptionStateEnum } from '@/models/order/interface';
import global from '@/utils/global';
import useUrlState from '@ahooksjs/use-url-state';
import { history, useDispatch } from '@umijs/max';
import { Button, NavBar } from 'antd-mobile';
import { CheckCircleFill, ExclamationCircleFill } from 'antd-mobile-icons';
import moment from 'moment';
import React, { useEffect } from 'react';

interface UrlQueryParam {
  subNo: string;
  state: SubscriptionStateEnum;
  payExpiredTime?: string;
}

const initUrlQueryParam: UrlQueryParam = {
  subNo: '',
  state: SubscriptionStateEnum.SIGNING,
};

const SubscriptionResult: React.FC = () => {
  const dispatch = useDispatch();
  const now = new Date().getTime();
  const oneDayMilliSeconds = 24 * 60 * 60 * 1000;
  const [urlParam] = useUrlState<UrlQueryParam>(initUrlQueryParam);
  // const now = new Date().getTime();
  // const oneDayMilliSeconds = 24 * 60 * 60 * 1000;
  // const deviceInfo: DeviceInfoWithPackage = useSelector(
  //   ({ device }: ConnectState) => device.deviceInfo,
  // );

  // const goBack = () => {
  //   // history.back();
  //   history.replace(
  //     `/package-selector?deviceId=${urlParam.deviceId}&deviceType=${urlParam.deviceType}`,
  //   );
  // };

  // const buryPointSubscribedResult = (detail: SubscribeDetail) => {
  //   global.aplus.buryPoint(PointTypeEnum.BUYNOW_SUBSCRIBE_RESULT, {
  //     Status: detail?.state || SubscriptionStateEnum.CANCELLED,
  //     Enter: sessionStorage.getItem('entrance') || EnterEnum.UNKNOWN,
  //     devicetype: detail?.deviceType || SuitableDeviceTypeEnum.D4sh,
  //     SKUID: detail?.skuId || 0,
  //   });
  // };

  // const redirectLogic = async (
  //   asyncSubscribeResult: AsyncSubscriptionInfo,
  //   hasPooling = false,
  // ) => {
  //   if (asyncSubscribeResult.state === SubscriptionStateEnum.ACTIVITY) {
  //     setUrlParam({ ...urlParam, state: SubscriptionStateEnum.ACTIVITY });
  //     return;
  //   }

  //   const currentTime = moment().valueOf();
  //   // waitTime时间外，则结束轮询
  //   if (currentTime - pageNowTime > waitTime) {
  //     // setLoading(false);
  //     return;
  //   }

  //   if (hasPooling) {
  //     // 否则每隔五秒就调一次同步订单的接口
  //     setTimeout(async () => {
  //       const _asyncOrder = await fetchSubscribeResult({
  //         subNo: urlParam.subNo,
  //         agreementNo: urlParam.agreementNo,
  //       });
  //       redirectLogic(_asyncOrder, hasPooling);
  //     }, 3 * 1000);
  //   }
  // };

  // const redirectPaymentResultLogic = (
  //   subscriptionOrderInfo: SubscriptionOrderInfo,
  //   hasPooling = false,
  // ) => {
  //   if (
  //     subscriptionOrderInfo &&
  //     subscriptionOrderInfo.state === OrderStatusEnum.PAY_SUCCEED
  //   ) {
  //     // history.replace({
  //     //   pathname: `/payment/result?deviceId=${urlParam.deviceId}&deviceType=${urlParam.deviceType}&orderId=${subscriptionOrderInfo.order_id}&state=${subscriptionOrderInfo.state}&errCode=0&errMsg=`,
  //     // });
  //     setPaymentResult(subscriptionOrderInfo);
  //     return;
  //   }
  //   if (
  //     subscriptionOrderInfo &&
  //     subscriptionOrderInfo.state === OrderStatusEnum.PAY_FAILED
  //   ) {
  //     // history.replace({
  //     //   pathname: `/payment/result?deviceId=${urlParam.deviceId}&deviceType=${urlParam.deviceType}&orderId=${subscriptionOrderInfo.order_id}&state=${subscriptionOrderInfo.state}&errCode=-1&errMsg=`,
  //     // });
  //     setPaymentResult(subscriptionOrderInfo);
  //     return;
  //   }
  //   const currentTime = moment().valueOf();
  //   // waitTime时间外，则结束轮询
  //   if (currentTime - startOrderSearchTime > waitTime) {
  //     // setLoading(false);
  //     setPaymentResult({
  //       order_id: '',
  //       state: OrderStatusEnum.PAY_FAILED,
  //     });
  //     return;
  //   }

  //   if (hasPooling) {
  //     // 否则每隔五秒就调一次同步订单的接口
  //     setTimeout(async () => {
  //       const _subscriptionOrderInfo = await fetchSubscriptionOrderInfo(
  //         urlParam.subNo,
  //       );
  //       redirectPaymentResultLogic(_subscriptionOrderInfo, hasPooling);
  //     }, 5 * 1000);
  //   }
  // };

  // const makePaymentResultAsyncApiPooling = async () => {
  //   if (urlParam.state !== SubscriptionStateEnum.ACTIVITY) {
  //     // 先同步签约结果
  //     const asyncSubscribeResult = await fetchSubscribeResult({
  //       subNo: urlParam.subNo,
  //       agreementNo: urlParam.agreementNo,
  //     });
  //     redirectLogic(asyncSubscribeResult, true);
  //   } else {
  //     // 如果签约结果已经是ACTIVITY了，则同步一下订单状态
  //     const subscriptionOrderInfo = await fetchSubscriptionOrderInfo(
  //       urlParam.subNo,
  //     );
  //     startOrderSearchTime = moment().valueOf();
  //     redirectPaymentResultLogic(subscriptionOrderInfo, true);
  //   }
  // };

  // // 获取签约详情
  // const requestSubscribeDetail = async (subNo: string) => {
  //   const subscribeDetail = await fetchSubscribeDetail(subNo);
  //   buryPointSubscribedResult(subscribeDetail);
  // };

  // const requestSubscriptionPaymentResult = () => {
  //   // const result = await fetch
  //   // history.replace(
  //   //   `/payment/result?deviceId=${urlParam.deviceId}&deviceType=${urlParam.deviceType}&orderId=${result.orderId}&state=${result.state}&errCode=0&errMsg=`,
  //   // );
  // };

  useEffect(() => {
    global.aplus.sendPV();
    dispatch({
      type: 'product/selectProductSkuById',
      payload: 0,
    });
  }, []);

  return (
    <div
      className={`h-full relative ${
        global.isiOS() ? 'padding-safe-area-ios' : ''
      } ${global.isAndroid() ? 'padding-safe-area-android' : ''} ${
        global.isHarmony() ? 'padding-safe-area-harmony' : ''
      }`}
    >
      <NavBar onBack={history.back}>
        <span className="active-color font-bold text-lg">订单结算</span>
      </NavBar>
      <div className="pt-32">
        {urlParam.state === SubscriptionStateEnum.ACTIVITY ? (
          <div className="flex items-center flex-col">
            <CheckCircleFill
              className="text-7xl"
              style={{ color: '#6cb636' }}
            />
            <p className="mt-5 text-xl active-color">签约成功</p>
            {urlParam.payExpiredTime && urlParam.payExpiredTime > now ? (
              <p>
                系统将在
                {urlParam.payExpiredTime - now > oneDayMilliSeconds
                  ? moment(urlParam.payExpiredTime).format('YYYY-MM-DD')
                  : '24小时内'}
                扣款...
              </p>
            ) : (
              ''
            )}
          </div>
        ) : (
          ''
        )}
        {urlParam.state === SubscriptionStateEnum.SIGNING ? (
          <div className="flex items-center flex-col">
            <ExclamationCircleFill
              className="text-7xl"
              style={{ color: '#e3762d' }}
            />
            <p className="mt-5 text-xl active-color">
              请在支付宝中完成签约流程
            </p>
          </div>
        ) : (
          ''
        )}
        {urlParam.state !== SubscriptionStateEnum.SIGNING &&
        urlParam.state !== SubscriptionStateEnum.ACTIVITY ? (
          <div className="flex items-center flex-col">
            <ExclamationCircleFill
              className="text-7xl"
              style={{ color: '#e3762d' }}
            />
            <p className="mt-5 text-xl active-color">签约失败</p>
          </div>
        ) : (
          ''
        )}
      </div>

      {urlParam.state === SubscriptionStateEnum.ACTIVITY ? (
        <div className="w-auto absolute left-4 right-5 bottom-14">
          <Button
            block
            shape="rounded"
            color="primary"
            style={{ backgroundColor: 'var(--color)' }}
            onClick={global.gotoMyCloudServiceList}
          >
            查看我的云服务
          </Button>
        </div>
      ) : (
        ''
      )}
    </div>
  );
};

export default SubscriptionResult;
