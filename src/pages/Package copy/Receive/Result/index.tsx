import global from '@/utils/global';
import { history, useDispatch } from '@umijs/max';
import { Button, NavBar } from 'antd-mobile';
import { CheckCircleFill, RightOutline } from 'antd-mobile-icons';
import { useEffect } from 'react';
import styles from './index.less';

const ReceiveResult = () => {
  const dispatch = useDispatch();
  // 查看服务详情
  const gotoServiceDetail = () => {
    global.gotoCurrentDeviceServicePage();
  };

  // 开始体验
  const start = () => {
    global.gotoCurrentDeviceIndexPage();
  };

  useEffect(() => {
    // 更新是否刷新首页商品列表的标识符
    dispatch({ type: 'utils/needProductListRefresh', payload: true });
    dispatch({
      type: 'product/selectProductSkuById',
      payload: 0,
    });
  }, []);

  return (
    <div
      className={`${styles.container} h-full relative ${
        global.isiOS() ? 'padding-safe-area-ios' : ''
      } ${global.isAndroid() ? 'padding-safe-area-android' : ''} ${
        global.isHarmony() ? 'padding-safe-area-harmony' : ''
      }`}
    >
      <NavBar onBack={history.back} className="mb-24">
        <span className="active-color font-bold text-lg" />
      </NavBar>
      <div className="flex items-center flex-col">
        <CheckCircleFill className="text-7xl" style={{ color: '#6cb636' }} />
        <p className="mt-5 text-xl active-color">领取成功</p>
      </div>
      <div className="w-auto absolute left-4 right-5 bottom-14">
        <div className={styles.serviceDetailButton} onClick={gotoServiceDetail}>
          查看服务详情{' '}
          <RightOutline className={styles.serviceDetailButtonIcon} />
        </div>
        <Button
          block
          shape="rounded"
          color="primary"
          style={{ backgroundColor: 'var(--color)' }}
          onClick={start}
          className={styles.startButton}
        >
          开始体验
        </Button>
      </div>
    </div>
  );
};

export default ReceiveResult;
