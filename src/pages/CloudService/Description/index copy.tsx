import { UrlStatusEnum } from '@/models/common.interface';
import global from '@/utils/global';
import { uuid } from '@/utils/uuid';
import useUrlState from '@ahooksjs/use-url-state';
import { history, useIntl, useParams } from '@umijs/max';
import { Image, NavBar } from 'antd-mobile';
import { AntOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { DescriptionTypeEnum } from '../interface';
import styles from './index.less';

interface DescriptionInfo {
  title: string;
  paragraphList: Array<{
    key: string;
    title: React.ReactNode;
    image?: string;
    subtitle?: React.ReactNode;
    content?: React.ReactNode;
    list?: React.ReactNode[];
  }>;
  src?: string;
}

const CloudServiceDescription: React.FC = () => {
  const [count, setCount] = useState(0);
  const [urlParam] = useUrlState<{
    hasNavBar: UrlStatusEnum;
    hasTitle: UrlStatusEnum;
  }>();
  const urlRestParam = useParams<{ type: DescriptionTypeEnum }>();
  const intl = useIntl();
  const descriptionInfo: { [key in DescriptionTypeEnum]: DescriptionInfo } = {
    [DescriptionTypeEnum.INTRODUCTION]: {
      title: intl.formatMessage({ id: 'description.introduction.title' }),
      paragraphList: [
        {
          key: uuid(),
          title: intl.formatMessage({
            id: 'description.introduction.paragraph1.title',
          }),
          content: intl.formatMessage({
            id: 'description.introduction.paragraph1.content',
          }),
          image: intl.formatMessage({
            id: 'description.introduction.paragraph1.image',
          }),
        },
        {
          key: uuid(),
          title: intl.formatMessage({
            id: 'description.introduction.paragraph2.title',
          }),
          subtitle: intl.formatMessage({
            id: 'description.introduction.paragraph2.content',
          }),
          image: intl.formatMessage({
            id: 'description.introduction.paragraph2.image',
          }),
        },
        {
          key: uuid(),
          title: intl.formatMessage({
            id: 'description.introduction.paragraph3.title',
          }),
          subtitle: intl.formatMessage({
            id: 'description.introduction.paragraph3.content',
          }),
          image: intl.formatMessage({
            id: 'description.introduction.paragraph3.image',
          }),
        },
        {
          key: uuid(),
          title: intl.formatMessage({
            id: 'description.introduction.paragraph4.title',
          }),
          subtitle: intl.formatMessage({
            id: 'description.introduction.paragraph4.content',
          }),
          image: intl.formatMessage({
            id: 'description.introduction.paragraph4.image',
          }),
        },
      ],
    },
    [DescriptionTypeEnum.DESCRIPTION_PROTOCOL]: {
      title: intl.formatMessage({ id: 'description.protocol.title' }),
      paragraphList: [],
      src: intl.formatMessage({ id: 'description.protocol.url' }),
    },
    [DescriptionTypeEnum.AUTO_RENEW_PROTOCOL]: {
      title: intl.formatMessage({ id: 'description.autoRenewProtocol.title' }),
      paragraphList: [],
      src: intl.formatMessage({ id: 'description.autoRenewProtocol.url' }),
    },
  };
  const title = urlRestParam.type
    ? descriptionInfo[urlRestParam.type].title
    : 'PETKIT Care+';

  useEffect(() => {
    global.aplus.sendPV();
    document.title = title;
  }, []);

  useEffect(() => {
    if (urlRestParam.type) return;
    history.back();
  }, [urlRestParam]);

  if (!urlRestParam.type) {
    return <></>;
  }

  return (
    <div
      className={classNames(
        'h-full overflow-auto relative no-scrollbar',
        styles.container,
        {
          'padding-safe-area-ios':
            urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isiOS(),
        },
        {
          'padding-safe-area-android':
            urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isAndroid(),
        },
        {
          'padding-safe-area-harmony':
            urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isHarmony(),
        },
        {
          'colorful-background':
            urlRestParam.type === DescriptionTypeEnum.INTRODUCTION &&
            urlParam.hasNavBar === UrlStatusEnum.ENABLE,
        },
      )}
      // className={`${styles.container} h-full overflow-auto relative ${
      //   urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isiOS() ? 'padding-safe-area-ios' : ''
      // } ${
      //   urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isAndroid()
      //     ? 'padding-safe-area-android'
      //     : ''
      // } ${
      //   urlRestParam.type === DescriptionTypeEnum.INTRODUCTION &&
      //   urlParam.hasNavBar === UrlStatusEnum.ENABLE
      //     ? 'colorful-background'
      //     : ''
      // }`}
    >
      {urlParam.hasNavBar === UrlStatusEnum.ENABLE ? (
        <NavBar
          right={
            count >= 3 ? (
              <AntOutline fontSize={20} onClick={global.setupVConsole} />
            ) : (
              ''
            )
          }
          onBack={history.back}
        >
          <span
            className="active-color font-bold text-lg"
            onClick={() => setCount(count + 1)}
          >
            {descriptionInfo[urlRestParam.type].title}
          </span>
        </NavBar>
      ) : null}
      {/* {urlRestParam.type === DescriptionTypeEnum.INTRODUCTION ? null : (
        <div className="font-semibold active-color text-xl text-center">
          {descriptionInfo[urlRestParam.type].title}
        </div>
      )} */}
      {urlRestParam.type === DescriptionTypeEnum.INTRODUCTION ? (
        <>
          {descriptionInfo[urlRestParam.type].paragraphList.map((item) => (
            <div className="m-4 mt-0 pt-4" key={item.key}>
              <div className="text-lg font-semibold mb-3 active-color">
                {item.title}
              </div>
              {item.subtitle ? (
                <div className="mb-3 text-xs default-color">
                  {item.subtitle}
                </div>
              ) : (
                ''
              )}
              {item.content ? (
                <div className="mb-3 text-sm normal-color">{item.content}</div>
              ) : (
                ''
              )}
              {item.list ? (
                <ol className="list-decimal pl-4">
                  {item.list.map((_item, index) => (
                    <li className="mb-3" key={String(index)}>
                      {_item}
                    </li>
                  ))}
                </ol>
              ) : null}
              {item.image ? (
                <Image lazy className="mb-3" src={item.image} />
              ) : (
                ''
              )}
            </div>
          ))}
        </>
      ) : (
        <iframe
          style={{
            height:
              urlParam.hasNavBar === UrlStatusEnum.ENABLE
                ? 'calc(100% - 45px)'
                : '100%',
            width: '100%',
          }}
          src={descriptionInfo[urlRestParam.type].src}
        />
      )}
    </div>
  );
};

export default CloudServiceDescription;
