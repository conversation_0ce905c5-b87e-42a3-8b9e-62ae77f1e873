import { ResultProductInfo } from '@/models/order/interface';

/**
 * =============================================
 * 计算结果（前端内部使用），用于全局使用
 */
export interface CalcResult {
  currentProduct: ResultProductInfo;
  purchaseProduct?: ResultProductInfo;
  signProduct?: ResultProductInfo;
  upgradeProduct?: ResultProductInfo;
  // 只有存在活动时，才会出现该值
  activityProduct?: ResultProductInfo;
  // 是否能购买
  purchase?: boolean;
  purchaseAmount?: number;
  // 是否能签约
  sign?: boolean;
  signAmount?: number;
  // 是否能补差价
  upgrade?: boolean;
  upgradeAmount?: number;
}
/**
 * =============================================
 */
