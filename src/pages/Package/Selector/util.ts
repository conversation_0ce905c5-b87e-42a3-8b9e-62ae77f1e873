// export const updateBenefitsBySku = (productSkuList: ProductSku[], defaultBenefitList: Benefit[]): ProductSku[] => {
//     if (!productSkuList || !productSkuList.length) return [];

//     return productSkuList.map((sku) => {
//         return {
//             ...sku,
//             benefits: sku.benefits.map(benefit => {
//                 const defaultBenefit = defaultBenefitList.find(db => benefit.id === db.)
//             })
//         };
//     });
// }
