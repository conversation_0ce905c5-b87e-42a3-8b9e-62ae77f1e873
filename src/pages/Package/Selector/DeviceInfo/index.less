@import '@/assets/styles/variables';

.container {
    padding: 16px;
    box-shadow: 0 4px 15px 0 rgba(245, 228, 193, 0.52);
    color: #fff;
}

.cardContainer {
    display: flex;
    margin-bottom: 16px;
    width: 100%;

    &:last-of-type {
        margin-bottom: 0
    }
}

.imageContainer {
    margin-right: 16px;
}

.content {
    margin-top: 5px;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: calc(100% - 80px - 16px);
    overflow: hidden;
}

.title {
    font-size: 18px;
    margin-bottom: 5px;
}

.subtitle {
    font-size: 12px;
    font-weight: 400;
}

.package {
    &Container {
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #eee;
        padding-top: 16px;
    }

    &Info {
        color: @default-color;
    }

    &Name {
        font-size: 14px;
        margin-bottom: 3px;
    }

    &Expiration {
        font-size: 12px;
    }

    &Status {
        font-size: 14px;
        color: #3378f8;
    }
}