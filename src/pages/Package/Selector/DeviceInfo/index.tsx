import { EmptyIcon } from '@/components/EmptyIcon';
import { DeviceInfoWithPackage } from '@/models/device/interface';
import { ErrorBlock, Image } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import styles from './index.less';

interface Props {
  className?: string;
  deviceInfo?: DeviceInfoWithPackage;
  showPendingPackageList?: boolean;
}

const DeviceInfo: React.FC<Props> = ({
  className = '',
  deviceInfo,
  showPendingPackageList = false,
}: Props) => {
  const imageSize = 80;
  const devicePackageInfo = useMemo(() => {
    return (
      deviceInfo?.effectiveProductInfo || deviceInfo?.expiredProductInfos?.[0]
    );
  }, [deviceInfo]);
  return (
    <section
      className={classNames(
        'rounded-lg bg-opacity-30',
        styles.container,
        className,
      )}
    >
      {deviceInfo ? (
        <>
          <div key="-1" className={styles.cardContainer}>
            <div className={classNames(styles.imageContainer)}>
              <Image
                src={deviceInfo.deviceUrl}
                width={imageSize}
                height={imageSize}
              />
            </div>
            <div className={classNames(styles.content)}>
              <h2
                className={classNames(
                  'w-full font-bold active-color truncate',
                  styles.title,
                )}
              >
                {deviceInfo.deviceName}
              </h2>
              <p className={classNames('default-color mb-1', styles.subtitle)}>
                {devicePackageInfo?.skuName || '未开通 PETKIT Care+ 服务'}
              </p>
              {devicePackageInfo?.skuId ? (
                <p className="text-xs default-color">
                  {devicePackageInfo.workIndate > dayjs().valueOf() ? (
                    <>
                      有效期至
                      {dayjs(devicePackageInfo.workIndate)
                        .tz(deviceInfo.zoneId)
                        .format('YYYY/MM/DD')}
                      {devicePackageInfo.subscribe ? '，到期后自动续费' : null}
                    </>
                  ) : (
                    <>已过期</>
                  )}
                </p>
              ) : null}
            </div>
          </div>
          {showPendingPackageList &&
            deviceInfo.pendingProductInfos.map((info, index) => (
              <div
                key={String(index)}
                className={classNames(
                  styles.cardContainer,
                  styles.packageContainer,
                )}
              >
                <div className={styles.packageInfo}>
                  <div className={styles.packageName}>{info.skuName}</div>
                  <div className={styles.packageExpiration}>
                    有效期至
                    {dayjs(info.workTime)
                      .tz(deviceInfo.zoneId)
                      .format('YYYY/MM/DD')}
                    ~
                    {dayjs(info.workIndate)
                      .tz(deviceInfo.zoneId)
                      .format('YYYY/MM/DD')}
                  </div>
                </div>
                <div className={styles.packageStatus}>待生效</div>
              </div>
            ))}
        </>
      ) : (
        <ErrorBlock
          image={<EmptyIcon style={{ margin: '0 auto' }} />}
          style={{
            margin: '0 auto',
            textAlign: 'center',
            '--image-height': '50px',
          }}
          title="暂无数据"
          description={null}
        />
      )}
    </section>
  );
};
export default DeviceInfo;
