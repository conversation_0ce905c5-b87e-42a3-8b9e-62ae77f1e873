.container {
  padding-bottom: 50px;

  &::-webkit-scrollbar {
    display: none;
  }

  :global(.adm-nav-bar-right) {
    display: flex;
    justify-content: flex-end;
  }
}

.rightActionPopover {

  :global(.adm-popover-inner) {
    width: 100px;
    overflow: hidden;
    box-shadow: 0 0 30px 0 rgba(51, 51, 51, 0.2) !important;
  }

  :global(.adm-popover-arrow) {
    display: none;
  }

  :global(.adm-popover-menu-item) {
    padding-left: 0;
    display: block;
  }

  :global(.adm-popover-menu-item-text) {
    font-size: 12px;
    color: #333;
    text-align: center;
    padding: 10px 0;
    width: 100px;
    line-height: 1.2em;
  }
}

.rightMenuButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20px;

  &:hover {
    cursor: pointer;
  }

  &Dot {
    width: 4px;
    height: 4px;
    background: #000;
    margin-bottom: 3px;
    border-radius: 50%;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 130px;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  box-shadow: 0 0 10px 0 rgba(188, 185, 177, 0.4);
}

.purchaseButton {
  // text-base rounded-3xl
  height: 50px;
  font-size: 16px;
  box-shadow: 0 3px 10px 0 rgba(255, 217, 139, 0.38);
  border-radius: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(137deg, #FFA93E 0%, #FF5271 100%);
  color: #fff;

  &Activity {}

  &:disabled {
    background: #999;
    box-shadow: none;
    opacity: 0.4;
    color: #333;

    & .buttonTip {
      color: #333;
    }
  }
}

.buttonText {}

.buttonTip {
  font-size: 12px;
  font-weight: 400;
  // color: #5e3000;
  color: #fff;
  opacity: 0.5;
  transform: scale(0.85);

  &Activity {}
}

.activityContainer {
  position: absolute;
  top: 130px;
  width: 314px;
  left: 50%;
  margin-left: -157px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activityContent {
  margin-bottom: 25px;
}

.activityClose {
  width: 32px;
  height: 32px;
  // font-size: 32px;
  // color: rgba(255, 255, 255, 0.9);
  // position: relative;
}

.popupBody {
  // padding-bottom: 20px;
}