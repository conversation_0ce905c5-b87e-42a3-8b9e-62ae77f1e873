import { ServiceTimeUnitEnum } from '@/models/product/interface';

interface SkuService {
  DEFAULT_ACTIVE_KEY: ServiceTimeUnitEnum;
  // 当前所选择的tab值数组
  selectedTabs: ServiceTimeUnitEnum[];
  // tabPane中的滚动条位置
  tabPaneScrollLeft: number;
  selectedProductSkuId: number;

  // 存储所选择的tab值
  setSelectedTabs: (value: ServiceTimeUnitEnum) => void;
}

const DEFAULT_ACTIVE_KEY = ServiceTimeUnitEnum.MONTH;

export const skuService: SkuService = {
  DEFAULT_ACTIVE_KEY,

  selectedTabs: [DEFAULT_ACTIVE_KEY],
  tabPaneScrollLeft: 0,
  selectedProductSkuId: 0,

  setSelectedTabs: (value: ServiceTimeUnitEnum) => {
    skuService.selectedTabs[0] = value;
  },
};
