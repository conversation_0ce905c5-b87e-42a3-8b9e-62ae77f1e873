import React from 'react';

interface ProgressIndicatorProps {
  isActive: boolean;
  progress: number;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  isActive,
  progress = 0,
}) => {
  if (!isActive) {
    // 未激活时显示传统圆形指示器
    return (
      <div className="w-1.5 h-1.5 rounded-full bg-gray-300 transition-all duration-300" />
    );
  }

  // 激活时显示长条形progress，高度与圆形指示器保持一致
  return (
    <div className="relative w-5 h-1.5 bg-gray-300 rounded-full overflow-hidden flex items-center">
      {/* 进度条 */}
      <div
        className="absolute left-0 top-0 h-full rounded-full transition-all duration-75 ease-linear"
        style={{
          width: `${progress}%`,
          backgroundColor: '#111',
          transformOrigin: 'left center',
        }}
      />
    </div>
  );
};

export default ProgressIndicator;
