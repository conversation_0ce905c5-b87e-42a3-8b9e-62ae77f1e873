import { Key } from '@/models/common.interface';
import {
  Benefit,
  Currency,
  ProductSkuActivityInfo,
  ProductSkuCapacityTypeEnum,
  RecordTypeEnum,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';

export interface OrderDuration extends Key {
  type: ServiceTimeUnitEnum;
  name: string;
  packageSkuList: PackageSkuInfo[];
}

export interface PackageSkuInfo {
  id: number;
  name: string;
  currency: Currency;
  // 显示价格；月价格
  price: number;
  // 总价格；月/年总价格
  totalPrice: number;
  linePrice: number;
  // pricePerDay: number;
  pricePerUnit: {
    serviceTimeUnit: string;
    price: number;
    symbol: string;
  };
  iconUrl: string;
  /**
   * @deprecated
   */
  capacities: Array<{
    type: ProductSkuCapacityTypeEnum;
    recordType: RecordTypeEnum;
  }>;
  /**
   * @deprecated
   */
  cycleTime: number;
  isRenew: boolean;
  firstPrice: number;
  relationSkuId: number;
  description: string;
  benefits: Benefit[];
  cornerMarkIcon: string;
  serviceTimeUnit: ServiceTimeUnitEnum;
  actPackage?: ProductSkuActivityInfo;
}
