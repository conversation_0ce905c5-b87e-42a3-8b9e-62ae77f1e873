import { ConnectState } from '@/models/connect';
import { Benefit, SuitableDeviceTypeEnum } from '@/models/product/interface';
import { history, useIntl, useSelector } from '@umijs/max';
import { RightOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import React from 'react';
import './index.less';
import styles from './index.less';

interface Props {
  deviceType: SuitableDeviceTypeEnum;
}

const SkuBenefit: React.FC<Props> = () => {
  const intl = useIntl();
  // const [image, setImage] = useState('');
  const benefits: Benefit[] = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku?.benefits || [],
  );

  const benefitTableData: string[][] = useSelector(
    ({ product }: ConnectState) => product.benefitTableData || [],
  );

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.title}>特权对比</div>
        <div className={styles.subtitle}>
          {(benefits || []).length ? (
            <p
              className="flex items-center text-xs cursor-pointer"
              onClick={() => history.push('/more-benefit')}
            >
              更多特权 <RightOutline className="ml-1" />
            </p>
          ) : null}
        </div>
      </div>
      {/* <Image src={image} /> */}
      {benefitTableData && benefitTableData.length ? (
        <div
          className="w-full border rounded-xl overflow-auto no-scrollbar"
          style={{ borderColor: '#FFD388' }}
        >
          <table className="border-collapse table-auto w-full">
            <thead>
              <tr>
                {(benefitTableData[0] || []).map((cell, index) => (
                  <th
                    className="text-center border-l px-2 py-2 text-xs overflow-hidden"
                    key={String(index)}
                    style={{
                      // width: `calc(100% / ${benefitTableData[0].length})`,
                      minWidth: 80,
                      // height: 40,
                      // lineHeight: '40px',
                    }}
                  >
                    {cell}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {benefitTableData.slice(1).map((row, rowIndex) => (
                <tr className="" key={String(rowIndex)} style={{ height: 40 }}>
                  {row.map((cell, cellIndex) => (
                    <td
                      key={String(cellIndex)}
                      className={classNames(
                        'border-l text-center text-xs px-2 py-2 overflow-hidden',
                        {
                          'scale-85': rowIndex === 0 && cellIndex > 1,
                        },
                      )}
                    >
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : null}
    </div>
  );
};

export default SkuBenefit;
