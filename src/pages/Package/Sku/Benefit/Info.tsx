import { Benefit } from '@/models/product/interface';
import { Image } from 'antd-mobile';
import React from 'react';

interface Props {
  benefitInfo: Benefit;
}

const BenefitInfo: React.FC<Props> = ({ benefitInfo }: Props) => {
  return (
    <div className="flex flex-col items-center">
      <Image
        className="rounded-xl mb-4"
        fit="fill"
        height={190}
        src={benefitInfo.image}
      />
      <h2 className="text-1.5xl active-color mb-3 font-semibold">
        {benefitInfo.name}
      </h2>
      <span className="text-sm default-color text-center">
        {benefitInfo.description}
      </span>
    </div>
  );
};

export default BenefitInfo;
