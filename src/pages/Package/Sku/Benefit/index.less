.container {
  margin-bottom: 36px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 22px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #111;
}

.subtitle {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;

  &Icon {
    width: 16px;
    height: 16px;
    font-size: 16px;
    color: #999;
  }
}

.body {
  border-radius: 8px;
  border: 1px solid #ffd388;
  overflow: hidden;
}

table {
  background: #fff;

  * {
    box-sizing: border-box;
  }

  tbody {
    tr:nth-of-type(even) {
      background: #FBFAF8;
    }

    td {
      text-wrap: wrap;
      white-space: normal;
    }
  }

}

// .tr {
//     height: 40px;

//     &:nth-of-type(2n + 1) {
//         background: #fbfaf8;
//     }
// }

// .thead {
//     .th {
//         color: #713006;
//         font-weight: 500;
//     }
// }

// .th,
// .td {
//     height: 40px;
//     text-align: center;
//     border-right: 1px solid rgba(240, 222, 184, 0.2);

//     &:last-of-type {
//         border-right: none;
//     }
// }