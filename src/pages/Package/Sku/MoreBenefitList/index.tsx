import { ConnectState } from '@/models/connect';
import { ProductSku } from '@/models/product/interface';
import global from '@/utils/global';
import { history, useSelector } from '@umijs/max';
import { NavBar, Tabs } from 'antd-mobile';
import React, { useEffect, useMemo, useState } from 'react';
import BenefitInfo from '../Benefit/Info';
import SkuBenefitItem from '../Benefit/Item';
import styles from './index.less';

const MoreBenefitList: React.FC = () => {
  const [activeKey, setActiveKey] = useState('');
  const selectedProductSku: ProductSku | undefined = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku,
  );

  const benefitList = useMemo(() => {
    // return (selectedProductSku?.benefits || []).filter(
    //   (benefit) => !benefit.handpick,
    // );
    return selectedProductSku?.benefits || [];
  }, [selectedProductSku]);

  useEffect(() => {
    global.aplus.sendPV();
  }, []);

  useEffect(() => {
    if (!benefitList || !benefitList.length) {
      history.back();
      return;
    }
    setActiveKey(`${benefitList[0].id}`);
  }, [benefitList]);

  if (!benefitList || !benefitList.length) return <></>;

  return (
    <div className="w-full h-full colorful-background">
      <NavBar
        style={{
          height:
            global.isiOS() || global.isAndroid() || global.isHarmony()
              ? 90
              : 60,
        }}
        onBack={history.back}
        className={`mb-4 ${global.isiOS() ? 'padding-safe-area-ios' : ''} ${
          global.isAndroid() ? 'padding-safe-area-android' : ''
        } ${global.isHarmony() ? 'padding-safe-area-harmony' : ''}`}
      >
        <span className="active-color font-bold text-lg">专属特权</span>
      </NavBar>
      <Tabs
        activeKey={activeKey}
        onChange={(key) => {
          setActiveKey(key);
        }}
        stretch={false}
        activeLineMode="fixed"
        className={styles.tabs}
      >
        {benefitList.map((item) => (
          <Tabs.Tab
            style={{ width: '25vw' }}
            title={
              <SkuBenefitItem
                key={item.id}
                url={item.icon}
                title={item.name}
                titleClassName="text-xs"
                layout="vertical"
                imageSize={46}
              />
            }
            key={item.id}
          >
            <div
              className="m-4 p-4 pt-6 pb-8 rounded-xl"
              style={{
                background: 'rgba(255,255,255,0.3)',
                boxShadow: '0px 2px 15px 0px rgba(245,228,193,0.8)',
              }}
            >
              <BenefitInfo benefitInfo={item} />
            </div>
          </Tabs.Tab>
        ))}
      </Tabs>
    </div>
  );
};
export default MoreBenefitList;
