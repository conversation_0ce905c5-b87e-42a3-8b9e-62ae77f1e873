import { ServiceTimeUnitEnum } from '@/models/product/interface';
import { Switch } from 'antd-mobile';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { OrderDuration } from '../interface';
import { skuService } from '../service';
import SkuSelectorList from './List';

interface Props {
  orderDurationList: OrderDuration[];
}

const Selector: React.FC<Props> = ({ orderDurationList = [] }: Props) => {
  const [activeKey, setActiveKey] = useState<ServiceTimeUnitEnum>(
    ServiceTimeUnitEnum.MONTH,
  );

  const [selectedOrderDuration, setSelectedOrderDuration] =
    useState<OrderDuration>(orderDurationList[0]);

  const getSkuSelectorListDom = () => {
    if (!selectedOrderDuration) return null;
    return (
      <SkuSelectorList
        currentTab={selectedOrderDuration.type}
        selectedTabKey={activeKey}
        packageSkuList={selectedOrderDuration.packageSkuList || []}
      />
    );
  };

  const changeServiceTimeUnit = (key: ServiceTimeUnitEnum) => {
    console.log(key);
    // 缓存所选的tab值
    skuService.setSelectedTabs(key);
    setActiveKey(key);
    // 重置tabPane的滚动距离
    skuService.tabPaneScrollLeft = 0;
    // 重置所选择的SKU的id
    skuService.selectedProductSkuId = 0;

    const _orderDuration = orderDurationList.find((item) => item.type === key);
    if (!_orderDuration) return;
    setSelectedOrderDuration(_orderDuration);
  };

  useEffect(() => {
    console.log(orderDurationList);
    if (!orderDurationList || !orderDurationList.length) return;

    const _orderDuration = orderDurationList.find(
      (item) => item.type === activeKey,
    );
    if (!_orderDuration) return;
    setSelectedOrderDuration(_orderDuration);
  }, [orderDurationList, activeKey]);

  if (!orderDurationList || !orderDurationList.length) return null;

  if (orderDurationList.length === 1) {
    return <>{getSkuSelectorListDom()}</>;
  }

  return (
    <div className="w-full">
      <h1 className="text-3xl text-center mb-6">选择您的订阅方案</h1>
      <div className="mb-6 flex items-center justify-center">
        <div
          className={classNames('mr-2.5 text-base', {
            'primary-text-color font-semibold':
              activeKey === ServiceTimeUnitEnum.MONTH,
            'default-color font-normal':
              activeKey !== ServiceTimeUnitEnum.MONTH,
          })}
          onClick={() => changeServiceTimeUnit(ServiceTimeUnitEnum.MONTH)}
        >
          包月
        </div>
        <Switch
          className="custom-duration-switch mr-2.5"
          checked={activeKey === ServiceTimeUnitEnum.YEAR}
          onChange={(checked) => {
            changeServiceTimeUnit(
              checked ? ServiceTimeUnitEnum.YEAR : ServiceTimeUnitEnum.MONTH,
            );
          }}
        />
        <div
          className={classNames('text-base', {
            'primary-text-color font-semibold':
              activeKey === ServiceTimeUnitEnum.YEAR,
            'default-color font-normal': activeKey !== ServiceTimeUnitEnum.YEAR,
          })}
          onClick={() => changeServiceTimeUnit(ServiceTimeUnitEnum.YEAR)}
        >
          包年
        </div>
      </div>
      <>{getSkuSelectorListDom()}</>
    </div>
  );
};
export default Selector;
