import { Key } from '@/models/common.interface';
import { ServiceTimeUnitEnum } from '@/models/product/interface';

export interface OrderDuration extends Key {
  type: ServiceTimeUnitEnum;
  name: string;
  packageSkuList: any[];
}

export interface PlanOption {
  id: string;
  name: string;
  type: 'annual' | 'monthly';
  price: number;
  currency: string;
  totalPrice?: number;
  monthlyEquivalent: number;
  isRecommended?: boolean;
  averagePricePerDevice?: number;
}

export interface DeviceShowcaseProps {
  className?: string;
}
