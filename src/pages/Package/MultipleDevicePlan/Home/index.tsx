import nikeIcon from '@/assets/nike.svg';
import PageContainer from '@/components/PageContainer';
import Protocol from '@/components/Protocol';
import { ConnectState } from '@/models/connect';
import { ProductSku, SuitableDeviceTypeEnum } from '@/models/product/interface';
import { transferProductSkuListToOrderDurationList } from '@/pages/Package/Sku/util';
import type { Dispatch } from '@umijs/max';
import { history, useDispatch, useSelector } from '@umijs/max';
import { Image } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import DeviceShowcase from './DeviceShowcase';
import styles from './index.less';
import { OrderDuration, PlanOption } from './interface';
import PlanSelector from './Selector';
import { planSelectorService } from './service';

interface QueryParam {
  deviceId: string;
  deviceType: SuitableDeviceTypeEnum;
}

const initQueryParam: QueryParam = {
  deviceId: '',
  deviceType: SuitableDeviceTypeEnum.D4sh,
};

interface MultipleDevicePlanSelectorProps {
  onPlanSelect?: (plan: PlanOption) => void;
  selectedPlanId?: string;
}

const MultipleDevicePlanSelector: React.FC<MultipleDevicePlanSelectorProps> = ({
  onPlanSelect,
  selectedPlanId: initialSelectedPlanId,
}) => {
  const dispatch: Dispatch = useDispatch();
  const scrollRef = useRef<HTMLDivElement>(null);
  const [selectorTabList, setSelectorTabList] = useState<OrderDuration[]>([]);

  const productSkuList: ProductSku[] = useSelector(
    ({ product }: ConnectState) => product.productSkuList,
  );
  const selectedProductSku: ProductSku | undefined = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku,
  );
  const slogens: { title: string; subtitle: string }[] = [
    {
      title: '支持关联3台设备',
      subtitle: '告别重复购买，一次开通，共享3台设备，更省心！',
    },
    {
      title: '买2赠1',
      subtitle: '仅需2台价格，就可享受3台服务，更省钱！',
    },
  ];

  // 移除硬编码数据，使用从 Redux store 获取的真实数据
  // const planOptions: PlanOption[] = [...] 已移除

  const [selectedPlanId, setSelectedPlanId] = useState<string>(
    initialSelectedPlanId || '',
  );

  const handlePlanSelect = (plan: PlanOption) => {
    setSelectedPlanId(plan.id);
    onPlanSelect?.(plan);
  };

  const handleGoBack = () => {
    history.back();
  };

  // 数据获取和处理
  useEffect(() => {
    if (!productSkuList || !productSkuList.length) {
      // 获取适合多设备计划的产品列表
      dispatch({
        type: 'product/requestBundleSkuList',
      });
      return;
    }
  }, [dispatch]);
  useEffect(() => {
    if (!productSkuList || !productSkuList.length) {
      return;
    }
    setSelectorTabList(
      transferProductSkuListToOrderDurationList(productSkuList),
    );
  }, [productSkuList]);

  // 页面滚动位置恢复
  useEffect(() => {
    if (!productSkuList || !productSkuList.length) return;
    scrollRef.current?.scrollTo({
      top: planSelectorService.pageScrollDistance,
    });
  }, [scrollRef, productSkuList]);

  const selectedPlan = selectedProductSku; // 使用真实的选中产品

  return (
    <PageContainer
      onBack={handleGoBack}
      className="colorful-background mb-7.5 font-semibold"
    >
      <div className="px-4">
        {/* 页面标题和特色功能 */}
        <div className="">
          <h1 className="text-3xl font-bold active-color mb-7.5 text-center">
            多设备·省心计划
          </h1>

          <div className="mb-13">
            {slogens.map((slogen) => (
              <div className="flex items-center justify-start mb-7 last-of-type:mb-0">
                <Image
                  alt="nike"
                  src={nikeIcon}
                  className="w-5 h-5 mr-3 ml-1.5"
                />
                <div>
                  <div className="font-semibold normal-color text-xl mb-0.5">
                    {slogen.title}
                  </div>
                  <div className="text-xs default-color">{slogen.subtitle}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 订阅方案选择 */}
        <section style={{ marginBottom: 36 }}>
          <PlanSelector orderDurationList={selectorTabList} />
        </section>

        {/* 设备展示和购买说明 */}
        <DeviceShowcase />

        {/* 服务协议说明 */}
        <div className="bg-white rounded-xl p-6">
          <Protocol />
        </div>

        {/* PETKIT Care+管理界面展示 */}
        <div
          className={classNames(
            'bg-white rounded-xl p-6',
            styles['petkit-care-management'],
          )}
        >
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
            <div className="text-center mb-4">
              <div className="flex items-center justify-between mb-4">
                <LeftOutline className="text-gray-400" />
                <h3 className="text-lg font-bold text-gray-900">
                  PETKIT Care+管理
                </h3>
                <div className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded">
                  打开
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div
                className={classNames(
                  'flex items-center justify-between p-3 bg-gray-50 rounded-lg',
                  styles['device-item'],
                )}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-xs text-blue-600">A</span>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      喵喵的健康数据 A
                    </h4>
                    <p className="text-xs text-gray-500">
                      3台设备·可绑定有效期至2026/08/31
                    </p>
                  </div>
                </div>
                <div
                  className={classNames(
                    'bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded',
                    styles['status-badge'],
                  )}
                >
                  订购中
                </div>
                <button className="text-blue-600 text-sm">Care+</button>
              </div>

              <div
                className={classNames(
                  'flex items-center justify-between p-3 bg-gray-50 rounded-lg',
                  styles['device-item'],
                )}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-xs text-blue-600">B</span>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      暂未关联设备
                    </h4>
                    <p className="text-xs text-gray-500">
                      3台设备·可绑定有效期至2026/08/31
                    </p>
                  </div>
                </div>
                <div
                  className={classNames(
                    'bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded',
                    styles['status-badge'],
                  )}
                >
                  订购中
                </div>
                <button className="text-blue-600 text-sm">去关联</button>
              </div>

              <div
                className={classNames(
                  'flex items-center justify-between p-3 bg-gray-50 rounded-lg',
                  styles['device-item'],
                )}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-xs text-blue-600">C</span>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      暂未关联设备
                    </h4>
                    <p className="text-xs text-gray-500">
                      3台设备·可绑定有效期至2026/08/31
                    </p>
                  </div>
                </div>
                <div
                  className={classNames(
                    'bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded',
                    styles['status-badge'],
                  )}
                >
                  订购中
                </div>
                <button className="text-blue-600 text-sm">去关联</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 底部购买按钮 */}
      {selectedPlan && (
        <div
          className={classNames(
            'fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4',
            styles['safe-area-bottom'],
          )}
        >
          <button
            className={classNames(
              'w-full text-white py-4 rounded-xl font-bold text-lg',
              styles['purchase-button'],
            )}
          >
            立即开通 ¥{selectedPlan.price?.price || 0}/月
          </button>
          <p className="text-center text-xs text-gray-500 mt-2">
            开通前阅读《PETKIT Care+ 服务说明协议》
          </p>
        </div>
      )}

      {/* 底部安全区域 */}
      <div className="h-20" />
    </PageContainer>
  );
};

export default MultipleDevicePlanSelector;
