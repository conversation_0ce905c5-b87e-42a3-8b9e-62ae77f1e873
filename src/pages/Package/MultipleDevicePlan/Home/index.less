// 多设备订阅计划页面样式


.recommend-badge {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.petkit-care-management {
  .device-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8fafc;
    }
  }

  .status-badge {
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
  }
}

.purchase-button {
  background: linear-gradient(135deg, #ea580c, #dc2626);

  &:active {
    transform: translateY(1px);
  }
}

// 适配安全区域
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}