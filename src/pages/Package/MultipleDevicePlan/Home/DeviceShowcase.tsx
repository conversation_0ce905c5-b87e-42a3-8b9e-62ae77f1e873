import React from 'react';
import { Image } from 'antd-mobile';
import { DeviceShowcaseProps } from './interface';

const DeviceShowcase: React.FC<DeviceShowcaseProps> = ({ className }) => {
  const devicePlaceholders = [
    { id: 1, name: '设备1' },
    { id: 2, name: '设备2' },
    { id: 3, name: '设备3' },
    { id: 4, name: '设备4' },
  ];

  return (
    <div className={`bg-white rounded-xl p-6 ${className || ''}`}>
      <div className="mb-4">
        <h3 className="text-lg font-bold text-gray-900 mb-2">购买说明</h3>
        <p className="text-sm text-gray-600 mb-4">
          该套餐支持3台适用范围内的设备同时畅享云服务。
        </p>
      </div>
      
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-2 gap-4">
          {devicePlaceholders.map((device) => (
            <div key={device.id} className="flex flex-col items-center">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                <Image
                  src=""
                  width={48}
                  height={48}
                  placeholder={
                    <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                      设备
                    </div>
                  }
                  fallback={
                    <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                      设备
                    </div>
                  }
                />
              </div>
              <span className="text-xs text-gray-500">{device.name}</span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="space-y-3">
        <p className="text-sm text-gray-600">
          如果您已购买云服务的单台设备，在点击"立即购买"后，可根据页面提示将其转换为多设备套餐。
        </p>
        <p className="text-sm text-gray-600">
          注：购买成功后，一旦设备完成绑定，服务将无法进行更换设备。
        </p>
        <p className="text-sm text-gray-600">
          购买完成后请在{' '}
          <span className="text-orange-600 font-medium">PETKIT Care+管理</span>{' '}
          自行绑定设备
        </p>
      </div>
    </div>
  );
};

export default DeviceShowcase;