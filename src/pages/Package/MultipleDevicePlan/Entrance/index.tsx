import { history } from '@umijs/max';
import React from 'react';
import './index.less';

export interface MultipleDevicePlanProps {}

const MultipleDevicePlanEntrance: React.FC<MultipleDevicePlanProps> = ({}) => {
  const goToEntrance = () => {
    history.push('/package/multiple-device-plan');
  };

  return (
    <div>
      {/* 标题部分 */}
      <h2 className="text-xl font-bold active-color mb-3">
        拥有多台PETKIT设备
      </h2>

      {/* 卡片部分 */}
      <div
        className="relative sku-item-bg-color rounded-xl p-4 cursor-pointer overflow-hidden"
        onClick={goToEntrance}
      >
        <div className="relative z-10 flex items-center justify-between">
          {/* 左侧：标题和副标题 */}
          <div className="flex-1">
            <h3 className="normal-color text-1.5xl font-bold mb-0.5">
              多设备·省心计划
            </h3>
            <p className="default-color text-xs">
              一次开通，至多3台设备都享受服务
            </p>
          </div>

          {/* 右侧：价格信息 */}
          <div className="flex items-end text-right">
            <div className="primary-color text-[28px] font-semibold mr-1.5 leading-tight">
              <span className="text-base">¥</span>11.67
            </div>
            <div className="default-color text-sm">起</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultipleDevicePlanEntrance;
