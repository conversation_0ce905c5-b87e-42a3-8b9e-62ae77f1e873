// 买二赠一组件样式
// 主要使用 Tailwind CSS，这里只定义 Tailwind 无法实现的复杂效果

// 自定义渐变动画（如果需要）
.gradient-animation {
  background: linear-gradient(135deg, #fb923c, #f97316, #ea580c);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 微光效果（可选）
.shimmer {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// 确保在小屏幕设备上的可读性
@media (max-width: 320px) {
  .responsive-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}