import PageContainer from '@/components/PageContainer';
import { ConnectState } from '@/models/connect';
import { BundleSku, ServiceTimeUnitEnum } from '@/models/product/interface';
import type { Dispatch } from '@umijs/max';
import { history, useDispatch, useSelector } from '@umijs/max';
import { Button, Form, Input, Picker, Toast } from 'antd-mobile';
import React, { useEffect, useState } from 'react';

interface BundleSkuFormData {
  name: string;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  deviceNumber: number;
  discountName: string;
}

const serviceTimeUnitOptions = [
  { label: '月', value: ServiceTimeUnitEnum.MONTH },
  { label: '年', value: ServiceTimeUnitEnum.YEAR },
  { label: '天', value: ServiceTimeUnitEnum.DAY },
];

const BundleSkuEdit: React.FC = () => {
  const dispatch: Dispatch = useDispatch();
  const [form] = Form.useForm<BundleSkuFormData>();
  const [loading, setLoading] = useState(false);

  // 从URL参数获取编辑的SKU ID（如果是编辑模式）
  const [skuId, setSkuId] = useState<number | null>(null);

  const bundleSkuList: BundleSku[] = useSelector(
    ({ product }: ConnectState) => product.bundleSkuList,
  );

  useEffect(() => {
    // 解析URL参数获取SKU ID
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');
    if (id) {
      setSkuId(Number(id));
      // 如果是编辑模式，加载现有数据
      const existingSku = bundleSkuList.find(sku => sku.id === Number(id));
      if (existingSku) {
        form.setFieldsValue({
          name: existingSku.name,
          serviceTime: existingSku.serviceTime,
          serviceTimeUnit: existingSku.serviceTimeUnit as ServiceTimeUnitEnum,
          deviceNumber: existingSku.deviceNumber || (existingSku as any).supportedDeviceNum || 1,
          discountName: existingSku.discountName || '',
        });
      }
    }
  }, [bundleSkuList, form]);

  const handleSubmit = async (values: BundleSkuFormData) => {
    setLoading(true);
    try {
      // 这里应该调用API保存数据
      // 由于接口定义调整，暂时只做表单验证和数据处理
      
      const bundleSkuData: Partial<BundleSku> = {
        ...values,
        id: skuId || Date.now(), // 新建时使用时间戳作为临时ID
        shortName: values.name,
        aliasName: values.name,
        cycleTime: values.serviceTime,
        level: 1,
        sort: 1,
        saleStatus: 1,
        price: {
          price: 0,
          linePrice: 0,
          unitPrice: 0,
          currency: {
            currencyCode: 'CNY',
            currencyName: '人民币',
            currencySymbol: '¥',
            minimumAmount: 0.01,
            paymentMethods: [],
            paymentPlatform: [],
            country: [],
          },
          firstPhasePrice: 0,
          isReNew: 0,
        },
        cornerMarkIcon: '',
      };

      console.log('保存BundleSku数据:', bundleSkuData);
      
      Toast.show({
        icon: 'success',
        content: skuId ? '更新成功' : '创建成功',
      });

      // 返回列表页面
      setTimeout(() => {
        history.back();
      }, 1000);

    } catch (error) {
      console.error('保存失败:', error);
      Toast.show({
        icon: 'fail',
        content: '保存失败，请重试',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    history.back();
  };

  return (
    <PageContainer
      title={skuId ? '编辑套餐' : '新建套餐'}
      onBack={handleGoBack}
      className="bg-gray-50"
    >
      <div className="px-4 py-6">
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <Form
            form={form}
            onFinish={handleSubmit}
            layout="vertical"
            footer={
              <div className="mt-6">
                <Button
                  type="submit"
                  color="primary"
                  size="large"
                  block
                  loading={loading}
                >
                  {skuId ? '更新套餐' : '创建套餐'}
                </Button>
              </div>
            }
          >
            <Form.Item
              name="name"
              label="套餐名称"
              rules={[{ required: true, message: '请输入套餐名称' }]}
            >
              <Input placeholder="请输入套餐名称" />
            </Form.Item>

            <Form.Item
              name="serviceTime"
              label="服务时长"
              rules={[
                { required: true, message: '请输入服务时长' },
                { 
                  validator: (_, value) => 
                    !value || (Number(value) > 0 && Number.isInteger(Number(value))) 
                      ? Promise.resolve() 
                      : Promise.reject(new Error('服务时长必须是大于0的整数'))
                },
              ]}
            >
              <Input type="number" placeholder="请输入服务时长" />
            </Form.Item>

            <Form.Item
              name="serviceTimeUnit"
              label="服务时长单位"
              rules={[{ required: true, message: '请选择服务时长单位' }]}
            >
              <Picker columns={[serviceTimeUnitOptions]}>
                {(value) => 
                  value ? serviceTimeUnitOptions.find(opt => opt.value === value[0])?.label : '请选择单位'
                }
              </Picker>
            </Form.Item>

            <Form.Item
              name="deviceNumber"
              label="支持设备数量"
              rules={[
                { required: true, message: '请输入支持设备数量' },
                { 
                  validator: (_, value) => 
                    !value || (Number(value) > 0 && Number.isInteger(Number(value))) 
                      ? Promise.resolve() 
                      : Promise.reject(new Error('设备数量必须是大于0的整数'))
                },
              ]}
            >
              <Input type="number" placeholder="请输入支持设备数量" />
            </Form.Item>

            <Form.Item
              name="discountName"
              label="优惠名称"
              rules={[{ required: true, message: '请输入优惠名称' }]}
            >
              <Input placeholder="请输入优惠名称，如：限时特惠、新用户专享等" />
            </Form.Item>
          </Form>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">说明</h3>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• 服务时长：套餐的有效期长度</li>
            <li>• 支持设备数量：该套餐最多可以绑定的设备数量</li>
            <li>• 优惠名称：用于在页面上显示的优惠信息</li>
          </ul>
        </div>
      </div>
    </PageContainer>
  );
};

export default BundleSkuEdit;