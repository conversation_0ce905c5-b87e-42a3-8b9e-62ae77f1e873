import {
  ConfirmPackageSkuInfo,
  ProductSku,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';
import { getEndTimeByServiceTimeInfo } from '@/pages/util';
import dayjs from 'dayjs';
import { serviceTimeUnitNames } from '../Sku/util';

const getOrderType = (
  serviceTime: number,
  serviceTimeUnit: ServiceTimeUnitEnum,
  isActive: boolean,
  isSubscribe?: boolean,
) => {
  if (isActive) {
    return `${serviceTime}${serviceTimeUnitNames[serviceTimeUnit]}`;
  }

  return `${isSubscribe ? '连续' : ''}包${
    serviceTimeUnit ? serviceTimeUnitNames[serviceTimeUnit] : ''
  }`;
};

const getPrice = (
  selectedProductSku: ProductSku,
  isSubscribe?: boolean,
  subscriptionProductSku?: ProductSku,
) => {
  console.log(
    'getPrice',
    selectedProductSku,
    isSubscribe,
    subscriptionProductSku,
  );
  // 自动续费套餐不存在活动
  if (selectedProductSku.actPackage && selectedProductSku.actPackage.price)
    return selectedProductSku.actPackage.price.amount;

  // 如果当前为订阅状态，则显示订阅套餐的价格
  if (isSubscribe && subscriptionProductSku)
    return (
      subscriptionProductSku.price.firstPhasePrice ||
      subscriptionProductSku.price.price
    );

  // 非订阅状态下，只有price可以显示
  return selectedProductSku.price.price;
};

export const getConfirmPackageSkuInfo = (
  selectedProductSku?: ProductSku,
  isSubscribe?: boolean,
  subscriptionProductSku?: ProductSku,
): ConfirmPackageSkuInfo => {
  const now = dayjs();
  const info: ConfirmPackageSkuInfo = {
    name: '',
    start: now,
    end: dayjs(),
    orderType: '',
    price: 0,
    priceCurrencySymbol: '$',
  };

  if (!selectedProductSku) return info;
  const serviceTime =
    selectedProductSku.actPackage?.serviceTime ||
    (isSubscribe
      ? subscriptionProductSku?.serviceTime || selectedProductSku.serviceTime
      : selectedProductSku.serviceTime);
  const serviceTimeUnit =
    selectedProductSku.actPackage?.serviceTimeUnit ||
    (isSubscribe
      ? subscriptionProductSku?.serviceTimeUnit ||
        selectedProductSku.serviceTimeUnit
      : selectedProductSku.serviceTimeUnit);

  info.name =
    selectedProductSku.actPackage?.actSkuName ||
    (isSubscribe
      ? subscriptionProductSku?.name || selectedProductSku.name
      : selectedProductSku.name);
  info.end = getEndTimeByServiceTimeInfo(serviceTime, serviceTimeUnit, now);
  info.orderType = getOrderType(
    serviceTime,
    serviceTimeUnit,
    !!selectedProductSku.actPackage,
    isSubscribe,
  );

  info.price = getPrice(
    selectedProductSku,
    isSubscribe,
    subscriptionProductSku,
  );

  info.linePrice = isSubscribe
    ? subscriptionProductSku?.price.linePrice ||
      selectedProductSku.price.linePrice
    : selectedProductSku.price.linePrice;

  info.priceCurrencySymbol =
    selectedProductSku.price.currency?.currencySymbol || '$';

  return info;
};
