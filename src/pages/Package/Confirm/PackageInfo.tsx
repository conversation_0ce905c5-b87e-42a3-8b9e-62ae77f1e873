import { DeviceInfoWithPackage } from '@/models/device/interface';
import { ConfirmPackageSkuInfo, ProductSku } from '@/models/product/interface';
import { CalcResult } from '@/pages/interface';
import dayjs, { Dayjs } from 'dayjs';
import React, { useMemo } from 'react';

interface Props {
  deviceInfo?: DeviceInfoWithPackage;
  // 不管切不切换到关联的SKU身上，都需要显示原始的套餐名称
  // packageName: string;
  // expirationDateRange: Dayjs[];
  // packageType: string;
  selectedProductSku?: ProductSku;
  isSubscribe: boolean;
  upgrade?: boolean;
  orderCalcResult?: CalcResult;
  subscriptionProductSku?: ProductSku;

  confirmPackageSkuInfo: ConfirmPackageSkuInfo;
}

const PackageInfo: React.FC<Props> = ({
  deviceInfo,
  upgrade,
  orderCalcResult,
  confirmPackageSkuInfo,
}: Props) => {
  const expirationDateRange: Dayjs[] = useMemo(() => {
    if (!deviceInfo) return [];

    // 不是补差价，同时没有计算结果，说明是首次购买
    if (!upgrade && !orderCalcResult) {
      return [confirmPackageSkuInfo.start, confirmPackageSkuInfo.end];
    }

    // 非首次购买，但也不是补差价，购买
    if (
      !upgrade &&
      orderCalcResult &&
      orderCalcResult.purchase &&
      orderCalcResult.purchaseProduct
    ) {
      return [
        dayjs(orderCalcResult.purchaseProduct.workTime).tz(deviceInfo.zoneId),
        dayjs(orderCalcResult.purchaseProduct.workInDate).tz(deviceInfo.zoneId),
      ];
    }

    // 非首次订阅，但也不补差价，订阅
    if (
      !upgrade &&
      orderCalcResult &&
      orderCalcResult.sign &&
      orderCalcResult.signProduct
    ) {
      return [
        dayjs(orderCalcResult.signProduct.workTime).tz(deviceInfo.zoneId),
        dayjs(orderCalcResult.signProduct.workInDate).tz(deviceInfo.zoneId),
      ];
    }

    // 此为补差价，orderCalcResult.upgradeServiceProduct必有值，如果没有就是重大缺陷
    if (upgrade && orderCalcResult && orderCalcResult.upgradeProduct) {
      return [
        dayjs(orderCalcResult.upgradeProduct.workTime).tz(deviceInfo.zoneId),
        dayjs(orderCalcResult.upgradeProduct.workInDate).tz(deviceInfo.zoneId),
      ];
    }

    return [];
  }, [orderCalcResult, upgrade, deviceInfo, confirmPackageSkuInfo]);

  return (
    <div>
      {/* <div className="flex items-center mb-2.5">
        <Image
          className="mr-2.5"
          width={imageSize}
          height={imageSize}
          src="http://sandbox.img5.petkit.cn/post/2022/11/3/6363a2e28543e76f7088ab63shAJLR1Ig"
        />
        <h3>设备2</h3>
      </div> */}
      <div>
        <dl className="default-color text-sm font-normal flex mb-2.5 justify-between">
          <dt>套餐名称：</dt>
          <dd>{confirmPackageSkuInfo.name}</dd>
        </dl>
        <dl className="default-color text-sm font-normal flex mb-2.5 justify-between">
          <dt>套餐服务有效期：</dt>
          <dd>
            {expirationDateRange[0].format('YYYY/MM/DD')}~
            {expirationDateRange[1].format('YYYY/MM/DD')}
          </dd>
        </dl>
        <dl className="default-color text-sm font-normal flex mb-5 justify-between">
          <dt>订单类型：</dt>
          <dd>{confirmPackageSkuInfo.orderType}</dd>
        </dl>
      </div>
    </div>
  );
};
export default PackageInfo;
