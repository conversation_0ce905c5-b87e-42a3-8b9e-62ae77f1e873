import { CheckOutline } from 'antd-mobile-icons';
import React, { useEffect } from 'react';

interface Props {
  isSelected: boolean;
  isPaying: boolean;
}

const AlipayPayment: React.FC<Props> = ({ isSelected, isPaying }: Props) => {
  useEffect(() => {
    if (!isPaying) return;

    console.log('支付宝开始支付');
    window.open(
      'http://bs-local-pay.devapi.petkit.com:11080/bs/order/createAlipay?X-Session=3372eb77c2b149f5b55638e1262d46ddgXuyyYbpq95R8VaAv43z&deviceId=1&deviceType=H3&skuId=36&payPlatform=Alipay',
      '_self',
    );
  }, [isPaying]);
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <i
          className="petkit-cloud-font petkit-cloud-zhifubaozhifu text-2xl mr-3"
          style={{ color: '#027aff' }}
        />{' '}
        支付宝支付
      </div>
      {isSelected ? <CheckOutline className="text-2xl" style={{ color: '#3378f8' }} /> : ''}
    </div>
  );
};

export default AlipayPayment;
