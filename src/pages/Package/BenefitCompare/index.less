@import '@/assets/styles/variables.less';

.planCompareContainer {
  margin: 40px 0;
  padding: 0 16px;

  .title {
    color: @active-color;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
    text-align: center;
  }

  .compareTable {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #E8E8E8;
    background-color: #FFFFFF;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .tableHeader {
      background-color: #FAFAFA;

      .headerCell {
        padding: 16px 12px;
        text-align: center;
        border-right: 1px solid #E8E8E8;

        &:first-child {
          background-color: transparent;
        }

        &:last-child {
          border-right: none;
        }

        .planName {
          color: @active-color;
          font-weight: bold;
          font-size: 14px;
          margin-bottom: 4px;
        }

        .planPrice {
          color: @active-color;
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 2px;
        }

        .planBilling {
          color: @secondary-text-color;
          font-size: 10px;
          opacity: 0.8;
        }
      }
    }

    .tableBody {
      .tableRow {
        border-top: 1px solid #E8E8E8;

        &:nth-child(even) {
          background-color: #FAFAFA;
        }

        .tableCell {
          padding: 16px 12px;
          text-align: center;
          border-right: 1px solid #E8E8E8;

          &:last-child {
            border-right: none;
          }

          &.featureNameCell {
            text-align: left;
            color: @secondary-text-color;
            font-size: 12px;
            background-color: #FFFFFF;
            font-weight: 500;
            min-width: 140px;

            .featureDescription {
              color: @secondary-text-color;
              font-size: 10px;
              margin-top: 4px;
              opacity: 0.7;
              line-height: 1.2;
            }
          }

          .featureValue {
            color: @active-color;
            font-size: 12px;
            font-weight: 600;
          }

          .checkIcon {
            width: 14px;
            height: 14px;
            margin: 0 auto;
          }
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    padding: 0 12px;

    .compareTable {
      .tableHeader .headerCell {
        padding: 12px 8px;
        min-width: 70px;

        .planName {
          font-size: 12px;
        }

        .planPrice {
          font-size: 14px;
        }

        .planBilling {
          font-size: 9px;
        }
      }

      .tableBody .tableRow .tableCell {
        padding: 12px 8px;

        &.featureNameCell {
          min-width: 120px;
          font-size: 11px;

          .featureDescription {
            font-size: 9px;
          }
        }

        .featureValue {
          font-size: 11px;
        }

        .checkIcon {
          width: 12px;
          height: 12px;
        }
      }
    }
  }
}