// 功能项定义
export interface FeatureItem {
  id: string;
  name: string;
  description?: string;
  icon?: string;
}

// 套餐功能值类型
export type FeatureValue = boolean | string | number;

// 套餐定义
export interface PlanItem {
  id: string;
  name: string;
  price: number;
  currency: string;
  billing: string;
  features: Record<string, FeatureValue>; // key 为 featureId，value 为该套餐对应的功能值
}

// 完整的对比数据结构
export interface BenefitCompareData {
  features: FeatureItem[]; // 功能列表（从接口获取）
  plans: PlanItem[]; // 套餐列表（从接口获取）
}