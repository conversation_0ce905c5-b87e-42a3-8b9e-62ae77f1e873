import { CloudStorageBenefit, ProductSku } from '@/models/product/interface';
import {
  BenefitCompareData,
  FeatureItem,
  FeatureValue,
  PlanItem,
} from './interface';

/**
 * 将 CloudStorageBenefit 数据转换为 BenefitCompareData 格式
 * @param cloudStorageBenefits 从接口获取的权益数据
 * @returns 转换后的对比表格数据
 */
export const transformCloudStorageToBenefitCompare = (
  cloudStorageBenefits: CloudStorageBenefit[],
): BenefitCompareData => {
  // 转换权益数据为 FeatureItem[]
  const features: FeatureItem[] = cloudStorageBenefits
    .sort((a, b) => a.sort - b.sort) // 按 sort 字段排序
    .map((benefit) => ({
      id: benefit.id.toString(),
      name: benefit.name,
      description: benefit.description || benefit.introduction,
      icon: benefit.icon,
    }));

  // 生成套餐数据
  const plans: PlanItem[] = generateMockPlans(cloudStorageBenefits);

  return {
    features,
    plans,
  };
};

/**
 * 生成四个套餐的 Mock 数据
 * 第一列使用真实的 attributeText，其余三列为 Mock 数据
 */
const generateMockPlans = (
  cloudStorageBenefits: CloudStorageBenefit[],
): PlanItem[] => {
  // 为每个权益生成 features 映射
  const generatePlanFeatures = (
    planType: 'free' | 'basic' | 'premium' | 'premiumPlus',
  ) => {
    const features: Record<string, FeatureValue> = {};

    cloudStorageBenefits.forEach((benefit) => {
      const featureId = benefit.id.toString();

      switch (planType) {
        case 'free':
          // 免费套餐使用真实的 attributeText
          features[featureId] =
            benefit.attributeText || getDefaultFeatureValue(benefit.name);
          break;
        case 'basic':
          // 基础套餐 Mock 数据
          features[featureId] = getMockFeatureValue(benefit.name, 'basic');
          break;
        case 'premium':
          // 高级套餐 Mock 数据
          features[featureId] = getMockFeatureValue(benefit.name, 'premium');
          break;
        case 'premiumPlus':
          // 高级+套餐 Mock 数据
          features[featureId] = getMockFeatureValue(
            benefit.name,
            'premiumPlus',
          );
          break;
      }
    });

    return features;
  };

  return [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      currency: '$',
      billing: '',
      features: generatePlanFeatures('free'),
    },
    {
      id: 'basic',
      name: 'Basic',
      price: 21.99,
      currency: '$',
      billing: 'billed annually',
      features: generatePlanFeatures('basic'),
    },
    {
      id: 'premium',
      name: 'Premium',
      price: 69.99,
      currency: '$',
      billing: 'billed annually',
      features: generatePlanFeatures('premium'),
    },
    {
      id: 'premiumPlus',
      name: 'Premium+',
      price: 119.99,
      currency: '$',
      billing: 'billed annually',
      features: generatePlanFeatures('premiumPlus'),
    },
  ];
};

/**
 * 根据权益名称和套餐类型生成 Mock 特性值
 */
const getMockFeatureValue = (
  benefitName: string,
  planType: 'basic' | 'premium' | 'premiumPlus',
): FeatureValue => {
  const name = benefitName.toLowerCase();

  // 24/7 直播和事件提醒对所有套餐都支持
  if (name.includes('live') || name.includes('alert')) {
    return true;
  }

  // 图片预览时长
  if (name.includes('image') || name.includes('preview')) {
    switch (planType) {
      case 'basic':
        return '7D';
      case 'premium':
        return '7D';
      case 'premiumPlus':
        return '30D';
    }
  }

  // 回放相关功能
  if (name.includes('playback') || name.includes('highlight')) {
    switch (planType) {
      case 'basic':
        return '1D';
      case 'premium':
        return '7D';
      case 'premiumPlus':
        return '30D';
    }
  }

  // VLOG 功能
  if (name.includes('vlog')) {
    return '7D'; // 所有付费套餐都是7天
  }

  // 播放质量
  if (name.includes('quality')) {
    switch (planType) {
      case 'basic':
        return 'HD';
      case 'premium':
        return 'HD';
      case 'premiumPlus':
        return 'Ultra HD';
    }
  }

  // 显示主题
  if (name.includes('theme') || name.includes('display')) {
    switch (planType) {
      case 'basic':
        return 'All';
      case 'premium':
        return 'All';
      case 'premiumPlus':
        return 'All';
    }
  }

  // 默认值：基础功能都支持
  return true;
};

/**
 * 获取权益的默认特性值（当 attributeText 为空时使用）
 */
const getDefaultFeatureValue = (benefitName: string): FeatureValue => {
  const name = benefitName.toLowerCase();

  if (name.includes('live') || name.includes('alert')) {
    return true;
  }

  if (name.includes('image') || name.includes('preview')) {
    return '1D';
  }

  if (name.includes('playback') || name.includes('highlight')) {
    return '30S';
  }

  if (name.includes('vlog')) {
    return '7D';
  }

  if (name.includes('quality')) {
    return 'SD';
  }

  if (name.includes('theme') || name.includes('display')) {
    return '1';
  }

  return false;
};

/**
 * 处理包年SKU列表：按level排序并去重
 * @param yearlySkuList 包年SKU列表
 * @returns 处理后的SKU列表（按level排序，每个level只保留一个）
 */
export const processYearlySkuList = (
  yearlySkuList?: ProductSku[],
): ProductSku[] => {
  if (!yearlySkuList || yearlySkuList.length === 0) {
    return [];
  }

  // 按 level 分组，每个 level 只保留第一个 sku
  const levelSkuMap: Record<number, ProductSku> = {};

  yearlySkuList.forEach((sku) => {
    if (!levelSkuMap[sku.level]) {
      levelSkuMap[sku.level] = sku;
    }
  });

  // 转换为数组并按 level 排序（level 值越小的排在越后面）
  return Object.values(levelSkuMap).sort((a, b) => b.level - a.level);
};

/**
 * 将SKU列表转换为BenefitCompareData格式
 * @param skuList 处理后的SKU列表
 * @returns 转换后的对比表格数据
 */
export const transformSkuListToBenefitCompare = (
  skuList: ProductSku[],
): BenefitCompareData => {
  if (!skuList || skuList.length === 0) {
    // 如果没有SKU数据，只返回Free套餐
    return {
      features: [],
      plans: [
        {
          id: 'free',
          name: 'Free',
          price: 0,
          currency: '$',
          billing: '',
          features: {},
        },
      ],
    };
  }

  // 收集所有权益数据作为 features
  const benefitMap: Record<string, FeatureItem> = {};
  const planFeatureMap: Record<string, Record<string, FeatureValue>> = {};

  // 先创建 Free 套餐
  planFeatureMap['free'] = {};

  skuList.forEach((sku) => {
    const planId = `level-${sku.level}`;
    planFeatureMap[planId] = {};

    console.log('sku.benefits', sku.id, sku.benefits);

    sku.benefits
      .filter((benefit) => benefit.name)
      .forEach((benefit) => {
        const featureId = benefit.id.toString();

        // 添加到 features 中（去重）
        if (!benefitMap[featureId]) {
          benefitMap[featureId] = {
            id: featureId,
            name: benefit.name,
            description: benefit.description,
            icon: benefit.icon,
          };
        }

        // 为当前套餐添加功能值
        planFeatureMap[planId][featureId] = benefit.handpick
          ? true
          : benefit.attributeSelectedText || benefit.attributeText || false;

        // 为 Free 套餐设置默认值（通常为false或基础值）
        if (!planFeatureMap['free'][featureId]) {
          planFeatureMap['free'][featureId] = getDefaultFeatureValueForSku(
            benefit.name,
          );
        }
      });
  });

  // 转换为 features 数组并按 sort 排序
  const features = Object.values(benefitMap).sort((a, b) => {
    // 尝试从原始数据中获取 sort 值，如果没有则按 id 排序
    const aSort =
      skuList
        .find((sku) =>
          sku.benefits.find((benefit) => benefit.id.toString() === a.id),
        )
        ?.benefits.find((benefit) => benefit.id.toString() === a.id)?.sort || 0;
    const bSort =
      skuList
        .find((sku) =>
          sku.benefits.find((benefit) => benefit.id.toString() === b.id),
        )
        ?.benefits.find((benefit) => benefit.id.toString() === b.id)?.sort || 0;
    return aSort - bSort;
  });

  // 创建 plans 数组
  const plans: PlanItem[] = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      currency: '$',
      billing: '',
      features: planFeatureMap['free'],
    },
  ];

  // 添加SKU对应的套餐
  skuList.forEach((sku) => {
    const planId = `level-${sku.level}`;
    plans.push({
      id: planId,
      name: sku.shortName || sku.name,
      price: sku.price.price,
      currency: sku.price.currency.currencySymbol,
      billing: 'billed annually',
      features: planFeatureMap[planId],
    });
  });

  return {
    features,
    plans,
  };
};

/**
 * 为SKU权益获取Free套餐的默认特性值
 */
const getDefaultFeatureValueForSku = (benefitName: string): FeatureValue => {
  const name = benefitName.toLowerCase();

  // 对于大部分功能，Free套餐默认不支持或提供基础版本
  if (name.includes('live') || name.includes('alert')) {
    return true; // 基础功能通常支持
  }

  if (name.includes('storage') || name.includes('cloud')) {
    return '1GB'; // 基础存储空间
  }

  if (name.includes('playback') || name.includes('video')) {
    return '30S'; // 基础回放时长
  }

  if (name.includes('quality')) {
    return 'SD'; // 基础画质
  }

  return false; // 默认不支持
};
