import { BenefitCompareData } from './interface';

// 模拟数据，符合图片内容
export const mockBenefitCompareData: BenefitCompareData = {
  features: [
    {
      id: 'liveStream247',
      name: '24/7 1080P Live Stream',
      description: 'Device limitations apply'
    },
    {
      id: 'eventAlert',
      name: 'Event Alert',
      description: 'Timely notifications for pet ...'
    },
    {
      id: 'imagePreview',
      name: 'Image Preview',
      description: 'Instant previews of pet activities'
    },
    {
      id: 'highlightPlayback',
      name: 'Highlight Playback',
      description: '30s pet activity clips'
    },
    {
      id: 'hdFullPlayback',
      name: 'HD Full Playback',
      description: 'Full pet activity recordings'
    },
    {
      id: 'hdVlog',
      name: 'HD VLOG',
      description: 'Quick overview of your pet\'s ...'
    },
    {
      id: 'playbackQuality',
      name: 'Playback Quality',
      description: '高清画质设置'
    },
    {
      id: 'displayTheme',
      name: 'Display Theme',
      description: 'Multiple styles to personalize'
    }
  ],
  plans: [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      currency: '$',
      billing: 'billed annually',
      features: {
        liveStream247: true,
        eventAlert: true,
        imagePreview: '1D',
        highlightPlayback: '30S',
        hdFullPlayback: '30S',
        hdVlog: '7D',
        playbackQuality: 'SD',
        displayTheme: 1
      }
    },
    {
      id: 'basic',
      name: 'Basic',
      price: 21.99,
      currency: '$',
      billing: 'billed annually',
      features: {
        liveStream247: true,
        eventAlert: true,
        imagePreview: '7D',
        highlightPlayback: '1D',
        hdFullPlayback: '1D',
        hdVlog: '7D',
        playbackQuality: 'HD',
        displayTheme: 'All'
      }
    },
    {
      id: 'premium',
      name: 'Premium',
      price: 69.99,
      currency: '$',
      billing: 'billed annually',
      features: {
        liveStream247: true,
        eventAlert: true,
        imagePreview: '7D',
        highlightPlayback: '7D',
        hdFullPlayback: '7D',
        hdVlog: '7D',
        playbackQuality: 'HD',
        displayTheme: 'All'
      }
    },
    {
      id: 'premiumPlus',
      name: 'Premium+',
      price: 119.99,
      currency: '$',
      billing: 'billed annually',
      features: {
        liveStream247: true,
        eventAlert: true,
        imagePreview: '30D',
        highlightPlayback: '30D',
        hdFullPlayback: '30D',
        hdVlog: '30D',
        playbackQuality: 'HD',
        displayTheme: 'All'
      }
    }
  ]
};