.container {
    padding-bottom: 50px;

    &::-webkit-scrollbar {
        display: none;
    }
}

.content {
    padding: 20px 16px;
}

.title {
    margin-bottom: 20px;
    color: #111;
    font-weight: 600;
    font-size: 18px;
    line-height: 1em;
}

.deviceInfo {
    background-color: #fff;
    margin-bottom: 16px;
}

.productInfo {
    font-size: 14px;
    color: #111;
}

.purchaseButton,
.signButton {
    // text-base rounded-3xl
    height: 50px;
    box-shadow: 0 3px 10px 0 rgba(255, 217, 139, 0.38);
    border-radius: 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    background: linear-gradient(137deg, #FFA93E 0%, #FF5271 100%);
    color: #fff;

    &Activity {}

    &:disabled {
        background: #999;
        box-shadow: none;
        opacity: 0.4;
        color: #333;

        & .buttonTip {
            color: #333;
        }
    }

    &Title {
        font-size: 16px;
        color: #fff;

        &Activity {}
    }

    &Subtitle {
        font-size: 10px;
        color: #fff;

        &Activity {}
    }
}

.pay {
    &Title {
        margin-bottom: 8px;
        font-size: 14px;
        color: #999;
    }

    &Card {
        border: 1px solid #F95300;
        box-shadow: 0 4px 15px 0 rgba(245, 228, 193, 0.5);
        border-radius: 12px;
        backdrop-filter: blur(6px);
        background: #FFFBEB;
        padding: 12px 16px 16px;
        margin-bottom: 16px;

        &:last-of-type {
            margin-bottom: 0;
        }

        &Title {
            color: #333;
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
            // display: flex;
            // align-items: center;
            margin-bottom: 10px;
        }

        &Subtitle {
            font-weight: 400;
            color: #999;
            line-height: 17px;
            font-size: 12px;
            display: flex;
            align-items: center;
            margin-left: 2px;

            &Icon {
                margin-left: 2px;

                &:hover {
                    cursor: pointer;
                }
            }
        }

        &Content {
            margin-bottom: 12px;
            font-size: 12px;
            font-weight: 400;
            color: #999;
            line-height: 17px;
        }

        &Footer {
            display: flex;
            justify-content: flex-end;
        }

        &Button {
            height: auto;
            font-size: 14px;
            padding: 6px 16px;
            font-weight: 600;
        }
    }
}