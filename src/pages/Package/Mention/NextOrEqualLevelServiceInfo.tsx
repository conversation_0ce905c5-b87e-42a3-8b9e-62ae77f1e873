import { DeviceInfoWithPackage } from '@/models/device/interface';
import { ProductSku } from '@/models/product/interface';
import { CalcResult } from '@/pages/interface';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import styles from './index.less';

interface Props {
  deviceInfo: DeviceInfoWithPackage;
  orderCalcResult: CalcResult;
  selectedProductSku: ProductSku;
}

const NextOrEqualLevelServiceInfo: React.FC<Props> = ({
  deviceInfo,
  orderCalcResult,
  selectedProductSku,
}: Props) => {
  const lastPendingProductSku = useMemo(() => {
    if (!deviceInfo) return;
    const { pendingProductInfos } = deviceInfo;
    if (!pendingProductInfos || !pendingProductInfos.length) return;
    return pendingProductInfos[pendingProductInfos.length - 1];
  }, [deviceInfo]);

  const nextProductSku = useMemo(() => {
    if (orderCalcResult.purchase) return orderCalcResult.purchaseProduct;

    if (orderCalcResult.sign) return orderCalcResult.signProduct;
  }, [orderCalcResult]);

  if (deviceInfo.pendingProductInfos && deviceInfo.pendingProductInfos.length) {
    if (orderCalcResult.sign) return <></>;

    return nextProductSku ? (
      <div className={styles.productInfo}>
        开通成功后，将在“
        <span className={classNames('warning-color')}>
          {lastPendingProductSku?.skuName || ''}
        </span>
        ”套餐到期后，
        <span className={classNames('warning-color')}>
          （
          {dayjs(nextProductSku.workTime)
            .tz(deviceInfo.zoneId)
            .format('YYYY/MM/DD')}
          ）
        </span>
        生效，新套餐有效期至
        <span className={classNames('warning-color')}>
          {orderCalcResult.activityProduct || orderCalcResult.purchaseProduct
            ? dayjs(nextProductSku.workInDate)
                .tz(deviceInfo.zoneId)
                .format('YYYY/MM/DD')
            : null}
        </span>
        。
      </div>
    ) : null;
  }

  if (!orderCalcResult.upgrade && nextProductSku)
    return (
      <div className={styles.productInfo}>
        更换“
        <span className={classNames('warning-color')}>
          {selectedProductSku.actPackage?.actSkuName || selectedProductSku.name}
        </span>
        ”套餐，将在原套餐到期后（
        <span className={classNames('warning-color')}>
          {dayjs(nextProductSku.workTime)
            .tz(deviceInfo.zoneId)
            .format('YYYY/MM/DD')}
        </span>
        ）生效，新套餐有效期至
        <span className={classNames('warning-color')}>
          {dayjs(nextProductSku.workInDate)
            .tz(deviceInfo.zoneId)
            .format('YYYY/MM/DD')}
        </span>
        {orderCalcResult.signProduct ? '，到期后自动续费' : '。'}
      </div>
    );

  return <></>;
};

export default NextOrEqualLevelServiceInfo;
