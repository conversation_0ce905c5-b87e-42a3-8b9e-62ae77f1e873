import PriceInfo from '@/components/PriceInfo';
import { DeviceInfoWithPackage } from '@/models/device/interface';
import { ProductSku } from '@/models/product/interface';
import { CalcResult } from '@/pages/interface';
import global from '@/utils/global';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React from 'react';
import { serviceTimeUnitNames } from '../Sku/util';
import styles from './index.less';

interface Props {
  orderCalcResult: CalcResult;
  selectedProductSku: ProductSku;
  goToPayment: (isUpdated: boolean) => void;
  buttonLoading: boolean;
  deviceInfo: DeviceInfoWithPackage;
}

const ActivationWay: React.FC<Props> = ({
  orderCalcResult,
  selectedProductSku,
  buttonLoading,
  goToPayment,
  deviceInfo,
}: Props) => {
  return (
    <section className={styles.pay}>
      <div className={styles.payTitle}>请选择开通方式</div>

      {/* 补差价购买/签约 */}
      {orderCalcResult.upgradeProduct ? (
        <div className={styles.payCard}>
          <div className={styles.payCardTitle}>
            支付
            <PriceInfo
              symbol={
                orderCalcResult.upgradeProduct?.currency?.currencySymbol ||
                global.DEFAULT_CURRENCY_SYMBOL
              }
              price={orderCalcResult.upgradeAmount || 0}
            />
            ，补差价购买
            <span className={classNames('warning-color')}>
              “
              {selectedProductSku.actPackage?.actSkuName ||
                selectedProductSku.name}
              ”
            </span>
            套餐{' '}
          </div>
          <div className={styles.payCardContent}>
            *补差价后，新套餐立即生效，有效期至
            {dayjs(
              (
                orderCalcResult.activityProduct ||
                orderCalcResult.upgradeProduct
              ).workInDate,
            )
              .tz(deviceInfo.zoneId)
              .format('YYYY/MM/DD')}
            {orderCalcResult.sign ? (
              <>
                ，到期按
                <PriceInfo
                  symbol={global.DEFAULT_CURRENCY_SYMBOL}
                  price={selectedProductSku.price.price}
                />
                /{serviceTimeUnitNames[selectedProductSku.serviceTimeUnit]}
                自动续费
              </>
            ) : (
              '。'
            )}
          </div>
          <div className={styles.payCardFooter}>
            <button
              type="button"
              className={classNames(
                styles.purchaseButton,
                styles.payCardButton,
              )}
              onClick={() => goToPayment(true)}
              disabled={buttonLoading}
            >
              <PriceInfo
                symbol={
                  orderCalcResult.upgradeProduct?.currency?.currencySymbol ||
                  global.DEFAULT_CURRENCY_SYMBOL
                }
                price={orderCalcResult.upgradeAmount || 0}
              />
              补差价购买
            </button>
          </div>
        </div>
      ) : null}
      {/* 平级/降级购买 */}
      {orderCalcResult.purchaseProduct ? (
        <div className={styles.payCard}>
          <div className={styles.payCardTitle}>
            直接购买
            <span className={classNames('warning-color')}>
              “
              {selectedProductSku.actPackage?.actSkuName ||
                selectedProductSku.name}
              ”
            </span>
            套餐
          </div>
          <div className={styles.payCardContent}>
            *购买成功后，新套餐将在原套餐到期后（
            {dayjs(
              (
                orderCalcResult.activityProduct ||
                orderCalcResult.purchaseProduct
              ).workTime,
            )
              .tz(deviceInfo.zoneId)
              .format('YYYY/MM/DD')}
            ）生效。
          </div>
          <div className={styles.payCardFooter}>
            <button
              type="button"
              className={classNames(
                styles.purchaseButton,
                styles.payCardButton,
              )}
              onClick={() => goToPayment(false)}
              disabled={buttonLoading}
            >
              <PriceInfo
                symbol={
                  (
                    orderCalcResult.activityProduct ||
                    orderCalcResult.purchaseProduct
                  ).currency?.currencySymbol || global.DEFAULT_CURRENCY_SYMBOL
                }
                price={orderCalcResult.purchaseAmount || 0}
              />
              直接购买套餐
            </button>
          </div>
        </div>
      ) : null}
      {/* 平级/降级签约 */}
      {orderCalcResult.signProduct ? (
        <div className={styles.payCard}>
          <div className={styles.payCardTitle}>
            直接购买
            <span className={classNames('warning-color')}>
              “{selectedProductSku.name}”
            </span>
            套餐
          </div>
          <div className={styles.payCardContent}>
            *购买成功后，新套餐将在原套餐到期后（
            {dayjs(orderCalcResult.signProduct.workTime)
              .tz(deviceInfo.zoneId)
              .format('YYYY/MM/DD')}
            ）生效。有效期至
            {dayjs(orderCalcResult.signProduct.workInDate)
              .tz(deviceInfo.zoneId)
              .format('YYYY/MM/DD')}
            ，到期自动续费。
          </div>
          <div className={styles.payCardFooter}>
            <button
              type="button"
              className={classNames(
                styles.purchaseButton,
                styles.payCardButton,
              )}
              onClick={() => goToPayment(false)}
              disabled={buttonLoading}
            >
              <PriceInfo
                symbol={
                  orderCalcResult.signProduct?.currency?.currencySymbol ||
                  global.DEFAULT_CURRENCY_SYMBOL
                }
                price={orderCalcResult.signAmount || 0}
              />
              直接购买套餐
            </button>
          </div>
        </div>
      ) : null}
    </section>
  );
};

export default ActivationWay;
