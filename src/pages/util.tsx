import { MAX_REST_DAYS } from '@/models/common.util';
import {
  DeviceInfoWithPackage,
  DevicePackageInfo,
} from '@/models/device/interface';
import {
  OrderCalcResult,
  SubscriptionOrderCalcResult,
} from '@/models/order/interface';
import { ProductSku, ServiceTimeUnitEnum } from '@/models/product/interface';
import classNames from 'classnames';
import dayjs, { Dayjs, ManipulateType } from 'dayjs';
import { CalcResult } from './interface';

export const pendingPackageInfoTip = (
  <>
    您当前还有“<span className={classNames('warning-color')}>待生效</span>
    ”的套餐，暂时无法购买此套餐
  </>
);

export const getAutoPackageInfoTip = (packageName: string) => {
  return (
    <>
      当前设备已拥有“
      <span className={classNames('warning-color')}>{packageName}</span>
      ”套餐，更换套餐请先取消自动续费
    </>
  );
};

export const getExistedPackageTip = (
  effectivePakcageInfo: DevicePackageInfo,
  zoneId: string,
) => {
  if (!effectivePakcageInfo) return '';
  const { skuName, workIndate } = effectivePakcageInfo;

  return (
    <>
      当前设备已拥有“
      <span className={classNames('warning-color')}>{skuName}</span>
      ”套餐，
      <span className={classNames('warning-color')}>
        有效期至{dayjs(workIndate).tz(zoneId).format('YYYY/MM/DD')}
      </span>
      ，暂时无法购买此套餐
    </>
  );
};

export const getEndTimeByServiceTimeInfo = (
  serviceTime: number,
  serviceTimeUnit: ServiceTimeUnitEnum,
  startTime: Dayjs = dayjs(),
  zoneId: string = 'Asia/Shanghai',
) => {
  let unit: ManipulateType = 'day';
  switch (serviceTimeUnit) {
    case ServiceTimeUnitEnum.DAY:
      unit = 'day';
      break;
    case ServiceTimeUnitEnum.MONTH:
      unit = 'month';
      break;
    case ServiceTimeUnitEnum.YEAR:
      unit = 'year';
      break;
    default:
      break;
  }

  return dayjs(startTime).tz(zoneId).add(serviceTime, unit);
};

export const getRestDays = ({
  start,
  end,
  serviceTime,
  serviceTimeUnit,
  zoneId,
}: {
  start?: Dayjs;
  end?: Dayjs;
  serviceTime?: number;
  serviceTimeUnit?: ServiceTimeUnitEnum;
  zoneId: string;
}): number => {
  if (end) {
    const startTime = (start || dayjs()).tz(zoneId).startOf('d');
    const endTime = end.tz(zoneId).endOf('d');
    return dayjs.duration(endTime.diff(startTime)).asDays();
  }

  if (serviceTime && serviceTimeUnit) {
    const startTime = start || dayjs();
    const endTime = getEndTimeByServiceTimeInfo(
      serviceTime,
      serviceTimeUnit,
      startTime,
      zoneId,
    );
    return dayjs.duration(endTime.diff(startTime)).asDays();
  }

  return 0;
};

// 当前为自动套餐(月/年)时的验证信息
const getValidCurrentSubscribeMessage = (
  currentPackageSku: DevicePackageInfo,
) => {
  const { skuName } = currentPackageSku;
  // 当前自动套餐的时候，提示先取消再购买/升级
  return getAutoPackageInfoTip(skuName);
};

// 获取平级/降级套餐购买的验证信息
const getValidPurchaseSkuMessage = (
  deviceInfo: DeviceInfoWithPackage,
  selectedProductSku: ProductSku,
) => {
  // 能进这里说明current就是非自套餐
  const { pendingProductInfos, effectiveProductInfo } = deviceInfo;
  const {
    actPackage,
    price: { isReNew },
  } = selectedProductSku;

  // 如果不存在当前处于生效中的套餐，则直接返回空字符串
  if (!effectiveProductInfo) {
    return '';
  }

  // 非自月/非自年 -> 非自月/非自年，或者是活动套餐时  无需处理
  if (!isReNew || !!actPackage) {
    const existPendingSubscribeSkus = pendingProductInfos.filter(
      (item) => item.subscribe,
    );
    if (existPendingSubscribeSkus && existPendingSubscribeSkus.length) {
      return getAutoPackageInfoTip(existPendingSubscribeSkus[0].skuName);
    }
    return '';
  }
  // 非自月/非自年 -> 自动月/自动年  如果存在pending服务，则不能购买
  if (pendingProductInfos && pendingProductInfos.length) {
    return pendingPackageInfoTip;
  }

  const restDays = getRestDays({
    start: dayjs().tz(deviceInfo.zoneId),
    end: dayjs(effectiveProductInfo.workIndate).tz(deviceInfo.zoneId),
    zoneId: deviceInfo.zoneId,
  });
  // 不存在pending服务，剩余周期不能大于30天
  if (restDays > MAX_REST_DAYS) {
    return getExistedPackageTip(effectiveProductInfo, deviceInfo.zoneId);
  }

  return '';
};

// 获取升级验证信息
const getValidUpgradeSkuMessage = (
  deviceInfo: DeviceInfoWithPackage,
  selectedProductSku: ProductSku,
) => {
  // 能进这里说明current就是非自套餐
  const { pendingProductInfos, effectiveProductInfo } = deviceInfo;
  const {
    actPackage,
    price: { isReNew },
  } = selectedProductSku;

  // 如果不存在当前处于生效中的套餐，则直接返回空字符串
  if (!effectiveProductInfo) {
    return '';
  }

  // 只要购买的套餐不是自动套餐，或者是活动套餐，则永远可以至少进行购买
  if (!isReNew || !!actPackage) {
    const existPendingSubscribeSkus = pendingProductInfos.filter(
      (item) => item.subscribe,
    );

    if (existPendingSubscribeSkus && existPendingSubscribeSkus.length) {
      return getAutoPackageInfoTip(existPendingSubscribeSkus[0].skuName);
    }

    return '';
  }

  // 非自月/非自年 升级到 自动月/自动年
  // 如果存在pending不能购买
  if (pendingProductInfos && pendingProductInfos.length) {
    return pendingPackageInfoTip;
  }

  // 如果当前套餐剩余时间大于30天，不能购买
  const restDays = getRestDays({
    start: dayjs().tz(deviceInfo.zoneId),
    end: dayjs(effectiveProductInfo.workIndate).tz(deviceInfo.zoneId),
    zoneId: deviceInfo.zoneId,
  });
  if (restDays > MAX_REST_DAYS) {
    return getExistedPackageTip(effectiveProductInfo, deviceInfo.zoneId);
  }

  return '';
};

// 购买/升级之前，获取验证信息
export const getValidDataMessage = (
  deviceInfo: DeviceInfoWithPackage,
  selectedProductSku: ProductSku,
) => {
  const { effectiveProductInfo } = deviceInfo;

  if (!effectiveProductInfo) return '';
  const { skuLevel: currentSkuLevel } = effectiveProductInfo;
  const { level: nextLevel } = selectedProductSku;

  // 当前套餐为自动套餐
  if (!!effectiveProductInfo.subscribe)
    return getValidCurrentSubscribeMessage(effectiveProductInfo);

  // 如果购买的套餐等级小于等于当前套餐 - 升级
  if (currentSkuLevel > nextLevel) {
    return getValidUpgradeSkuMessage(deviceInfo, selectedProductSku);
  }

  // 如果购买的套餐等级大于当前套餐 - 平级/降级
  if (currentSkuLevel <= nextLevel) {
    return getValidPurchaseSkuMessage(deviceInfo, selectedProductSku);
  }

  return '';
};

/**
 * =============================================
 * 计算结果（前端内部使用），用于全局使用
 */
// 将非自套餐的支付计算结果转为页面用的计算结果数据结构
export const transferOrderCalcResultToCalcResult = (
  result: OrderCalcResult,
): CalcResult => {
  const calcResult: CalcResult = {
    currentProduct: result.currentServiceProduct,
    purchaseProduct: result.purchaseServiceProduct,
    upgradeProduct: result.upgradeServiceProduct,
    // 是否能购买
    purchase: !!result.purchase,
    purchaseAmount: result.purchaseServiceProduct?.amount || 0,
    // 是否能升级
    upgrade: !!result.upgrade,
    upgradeAmount: result.upgradeServiceProduct?.amount || 0,
  };
  return calcResult;
};

// 将自动套餐的支付计算结果转为页面用的计算结果数据结构
export const transferSubscriptionCalcResultToCalcResult = (
  result: SubscriptionOrderCalcResult,
): CalcResult => {
  const calcResult: CalcResult = {
    currentProduct: result.currentProduct,
    signProduct: result.signProduct,
    upgradeProduct: result.upgradeProduct,
    // 是否能签约
    sign: !!result.sign,
    signAmount: result.signAmount,
    // 是否能升级
    upgrade: !!result.upgrade,
    upgradeAmount: result.upgradeAmount,
  };
  return calcResult;
};
/**
 * =============================================
 */

/**
 * =============================================
 * 效期相关计算逻辑
 */
// export const getStartTime = (
//   pendingProductInfos: DevicePackageInfo[] = [],
//   effectiveProductInfo?: DevicePackageInfo,
// ) => {
//   // 如果存在pending的服务，则先使用pending中最后那个服务的结束日期+1作为起始日期
//   if (pendingProductInfos && pendingProductInfos.length) {
//     return dayjs(pendingProductInfos[pendingProductInfos.length - 1].workIndate)
//       .tz(deviceInfo.zoneId)
//       .add(1, 'day');
//   }

//   // 如果没有pending的服务，则使用当前激活的服务的结束日期+1作为起始日期
//   if (effectiveProductInfo) {
//     return dayjs(effectiveProductInfo.workIndate)
//       .tz(deviceInfo.zoneId)
//       .add(1, 'day');
//   }

//   // 如果既没有pending，也没有当前激活的，则用今天为起始日期
//   return dayjs();
// };
/**
 * =============================================
 */

/**
 * =============================================
 * 根据ProductSku获取对应服务的价格货币单位
 */
export const getServiceProductSkuPriceCurrency = (
  productSku?: ProductSku,
  lowerCase = false,
): string => {
  if (!productSku) return 'USD';
  const { currencyCode } = productSku.price.currency;
  if (lowerCase) return currencyCode.toLowerCase();
  return currencyCode;
};
/**
 * =============================================
 */
