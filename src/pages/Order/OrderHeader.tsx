import { Order, OrderStatusEnum, PayTypeEnum } from '@/models/order/interface';
import { orderStatusObj, payTypeObj } from '@/models/order/util';
import { Tag, Toast } from 'antd-mobile';
import copy from 'copy-to-clipboard';
import moment from 'moment';
import React from 'react';

interface Props {
  order: Order;
  isManualCancelled: boolean;
  payExpireTime: number;
}

const OrderHeader: React.FC<Props> = ({
  order,
  isManualCancelled,
  payExpireTime,
}: Props) => {
  const copyOrderNo = (text: string) => {
    const copyResult = copy(text);
    if (copyResult) {
      Toast.show('复制成功');
    } else {
      Toast.show('复制失败');
    }
  };

  return (
    <>
      <div className="flex justify-between items-center mb-3.5">
        <h2 className="active-color text-base font-bold">
          {order.couponNickName || order.skuName}
        </h2>
        {/* 免费领取不展示订单状态了 */}
        {order.platform === PayTypeEnum.EXPERIENCE ? null : (
          <p
            className={`text-sm font-normal ${
              order.state === OrderStatusEnum.PAYING &&
              payExpireTime >= moment().valueOf() &&
              (!isManualCancelled || !order.upgrade)
                ? 'price-color'
                : 'default-color'
            }`}
          >
            {/* 如果当前时间已经超过剩余支付时间，且当前状态为CREATED的时候，就会直接显示已取消；追加一条：当订单为补差价订单时，也会显示为已取消 */}
            {order.platform !== PayTypeEnum.EXCHANGECOUPON
              ? (order.state === OrderStatusEnum.PAYING &&
                  payExpireTime < moment().valueOf()) ||
                isManualCancelled
                ? orderStatusObj[OrderStatusEnum.CANCELLED]
                : orderStatusObj[order.state]
              : null}
          </p>
        )}
      </div>
      <div>
        <dl className="default-color text-xs font-normal flex mb-2.5 items-top justify-between">
          <dt className="flex-grow flex-shrink-0">订单编号：</dt>
          <dd className="flex-grow-0 overflow-hidden w-full flex">
            <p
              className="mb-0 break-words"
              style={{ width: 'calc(100% - 30px)' }}
            >
              {order.orderId}
            </p>
            <a
              className="text-right"
              style={{ display: 'block', color: '#3378f8', width: 30 }}
              onClick={() => copyOrderNo(order.orderId)}
            >
              复制
            </a>
          </dd>
        </dl>
        <dl className="default-color text-xs font-normal flex mb-2.5 items-center justify-between">
          <dt>下单时间：</dt>
          <dd>{moment(order.createTime).format('YYYY-MM-DD')}</dd>
        </dl>
        <dl className="default-color text-xs font-normal flex mb-2.5 items-center justify-between">
          <dt>支付方式：</dt>
          <dd>
            {payTypeObj[order.platform as PayTypeEnum]}{' '}
            {order.isRenew ? (
              <Tag
                className="ml-2"
                style={{ color: '#2d74fc' }}
                color="rgba(51, 120, 248, 0.1)"
              >
                自动续费
              </Tag>
            ) : (
              ''
            )}
          </dd>
          {/* <dd></dd> */}
        </dl>
      </div>
    </>
  );
};

export default OrderHeader;
