import PayComp from '@/components/PayComp';
import { getTwoDigitNumber } from '@/models/common.util';
import { fetchCancelOrder, fetchPaymentResult } from '@/models/order/fetch';
import {
  Order,
  OrderCreationResult,
  OrderStatusEnum,
  PayTypeEnum,
} from '@/models/order/interface';
import global from '@/utils/global';
import { useCountDown } from 'ahooks';
import { Button, Card, Dialog, Toast } from 'antd-mobile';
import classNames from 'classnames';
import moment from 'moment';
import { useEffect, useState } from 'react';
import OrderHeader from './OrderHeader';
import styles from './index.less';

interface Props {
  className: string;
  order: Order;
}

const OrderItem: React.FC<Props> = ({ order, className }: Props) => {
  const [payExpireTime, setPayExpireTime] = useState(0);
  const [loading, setLoading] = useState(false);
  const [, formattedRes] = useCountDown({
    targetDate: moment(order.payExpireTime).format('YYYY-MM-DD HH:mm:ss'),
  });
  const { days, hours, minutes, seconds } = formattedRes;
  const [isManualCancelled, setIsManualCancelled] = useState(false);

  // 同步支付结果
  const requestAsyncPaymentResult = async (_orderId: string) => {
    const result = await fetchPaymentResult(_orderId);
    if (result.state === OrderStatusEnum.PAY_SUCCEED) {
      // 跳转到我的云服务
      global.gotoCurrentDeviceServicePage();
    } else if (result.state === OrderStatusEnum.PAYING) {
      // history.replace(`/order/list`);
      Dialog.clear();
    }
  };

  // 取消订单
  const cancelOrder = async (orderId: string) => {
    setLoading(true);
    try {
      await fetchCancelOrder(orderId);
      setIsManualCancelled(true);
      Toast.show('订单取消成功');
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 展示是否完成支付的弹框
  const showPaymentResultDialog = (orderId: string) => {
    Dialog.clear();
    Dialog.confirm({
      // title: '确认',
      content: '您是否已完成支付？',
      confirmText: (
        <span style={{ fontSize: 14, fontWeight: 'normal' }}>已完成支付</span>
      ),
      cancelText: (
        <span style={{ fontSize: 14, fontWeight: 'bold', color: '#999' }}>
          支付碰到问题
        </span>
      ),
      onCancel: () => requestAsyncPaymentResult(orderId),
      onConfirm: () => requestAsyncPaymentResult(orderId),
    });
  };

  // 判断页面是是否存在待确认的支付订单
  const confirmOrderResult = (_order: Order) => {
    const resultStr = sessionStorage.getItem('payResultInfo');
    sessionStorage.removeItem('payResultInfo');
    if (!resultStr) return;
    const result: OrderCreationResult = JSON.parse(resultStr);
    if (
      !result ||
      result.orderId !== _order.orderId ||
      result.deviceId !== _order.device.deviceId ||
      result.deviceType !== _order.device.deviceType
    )
      return;
    if (_order.state === OrderStatusEnum.PAY_SUCCEED) {
      // 跳转到我的云服务
      global.gotoCurrentDeviceServicePage();
    } else if (_order.state === OrderStatusEnum.PAYING) {
      showPaymentResultDialog(result.orderId);
    }
  };

  useEffect(() => {
    // console.log('order list item useEffect', order);
    // requestOrderList(listParam);
    confirmOrderResult(order);
  }, [order]);

  useEffect(() => {
    if (!order || !order.payExpireTime) return;
    setPayExpireTime(moment(order.payExpireTime).valueOf());

    // if (order.state === OrderStatusEnum.PAY_SUCCEED && ) {}
  }, [order]);

  return (
    <Card
      className={classNames(`pl-4 pr-4`, className, styles.item)}
      style={{ boxShadow: '0 2px 11px 0 rgba(0, 0, 0, 0.04)' }}
      title={
        <OrderHeader
          payExpireTime={payExpireTime}
          isManualCancelled={isManualCancelled}
          order={order}
        />
      }
      headerClassName="pt-4 pb-4 w-full"
      bodyClassName="pt-4 pb-4"
    >
      <div className="flex justify-between items-center">
        <div className="default-color text-xs">
          {order.state === OrderStatusEnum.PAYING &&
          payExpireTime >= moment().valueOf() &&
          !isManualCancelled &&
          !order.upgrade ? (
            <>
              剩余支付时间:{' '}
              {`${getTwoDigitNumber(days * 24 + hours)}:${getTwoDigitNumber(
                minutes,
              )}:${getTwoDigitNumber(seconds)}`}
            </>
          ) : (
            ''
          )}
        </div>
        <div>
          <div className="text-right">
            <span className="default-color text-xs mr-1">支付金额:</span>
            <span className="active-color text-base font-semibold">
              {order.currencyInfo.currencySymbol} {order.amount}
            </span>
          </div>
          {(order.state === OrderStatusEnum.REFUND_SUCCEED ||
            order.state === OrderStatusEnum.REFUNDING) &&
          order.refundedAmount ? (
            <div>
              <span className="default-color text-xs mr-1">退款金额:</span>
              <span className="active-color text-base font-semibold">
                {order.currencyInfo.currencySymbol} {order.refundedAmount}
              </span>
            </div>
          ) : null}
        </div>
      </div>
      {/* 如果当前时间已经超过剩余支付时间，且当前状态为CREATED的时候，就会直接显示已取消；追加一条：为补差价订单时，也显示取消状态 */}
      {order.state === OrderStatusEnum.PAYING &&
      payExpireTime >= moment().valueOf() &&
      !isManualCancelled ? (
        <div className="text-right mt-3">
          <Button
            disabled={loading}
            className="pl-4 pr-4 pt-1 pb-1"
            style={{
              borderColor: '#ddd',
              color: '#999',
              marginRight: '0.625rem',
            }}
            shape="rounded"
            color="primary"
            fill="outline"
            onClick={() => cancelOrder(order.orderId)}
          >
            取消订单
          </Button>
          {!order.upgrade ? (
            <PayComp
              deviceId={order.device.deviceId}
              deviceType={order.device.deviceType}
              payTimes="notFirst"
              order={order}
              createPaySuccessFunction={(
                orderId: string,
                payType: PayTypeEnum,
              ) => {
                setTimeout(() => {
                  if (global.isiOS() && payType === PayTypeEnum.WEIXIN)
                    showPaymentResultDialog(orderId);
                }, 800);
              }}
            />
          ) : null}
        </div>
      ) : (
        ''
      )}
    </Card>
  );
};
export default OrderItem;
