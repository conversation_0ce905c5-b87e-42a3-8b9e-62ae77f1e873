import orderEmptyPng from '@/assets/order-empty.png';
import { fetchOrderList } from '@/models/order/fetch';
import { Order, OrderListParam } from '@/models/order/interface';
import { initOrderListParam } from '@/models/order/util';
import { SuitableDeviceTypeEnum } from '@/models/product/interface';
import global from '@/utils/global';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { ErrorBlock, InfiniteScroll, NavBar, Toast } from 'antd-mobile';
import { AntOutline } from 'antd-mobile-icons';
import { useCallback, useEffect, useMemo, useState } from 'react';
import OrderItem from '../Item';
import './index.less';

const OrderList = () => {
  let loadingList = false;
  const [urlParam] = useUrlState<{
    deviceId: number;
    deviceType: SuitableDeviceTypeEnum;
  }>();
  const [count, setCount] = useState(0);
  // const [loading, setLoading] = useState(false);
  const [orderList, setOrderList] = useState<Order[]>([]);
  const [listParam, setListParam] = useState<OrderListParam>({
    ...initOrderListParam,
  });
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [loading, setLoading] = useState(false);

  const headerHeight = useMemo(() => {
    if (global.isiOS()) {
      return 90;
    }

    if (global.isAndroid() || global.isHarmony()) {
      return 105;
    }

    return 60;
  }, []);

  const requestOrderList = async (_listParam: OrderListParam) => {
    if (orderList.length >= pagination.total && pagination.total > 0) {
      Toast.show({ content: '已经到底啦~', duration: 2000 });
      return;
    }
    let toastRef: any;
    if (global.isInApp()) {
      toastRef = Toast.show({
        icon: 'loading',
        content: '加载中…',
        duration: 0,
      });
    }
    try {
      setLoading(true);
      const { items, ...rest } = await fetchOrderList(_listParam);
      loadingList = false;
      // setPagination({ ...rest, limit: listParam.limit, offset: listParam.offset });
      setPagination({ ...rest });
      const _list = items.map((item) => ({ ...item }));
      if (listParam.offset === 0) {
        setOrderList(_list);
      } else {
        setOrderList([...orderList, ...items]);
      }
      toastRef?.close();
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    if (urlParam.deviceId || urlParam.deviceType) {
      history.back();
      return;
    }
    global.closeWebview();
  };

  const loadMore = useCallback(async () => {
    if ((listParam.offset === 0 && pagination.total === 0) || loading) return;
    if (pagination.total > pagination.offset + pagination.limit) {
      loadingList = true;
      if (loadingList)
        setListParam({
          ...listParam,
          offset: pagination.offset + listParam.limit,
        });
    }
  }, [listParam, pagination, loading]);
  // const loadMore = useCallback(async () => {
  //   console.log(pagination, pagination.offset, listParam);
  //   if (listParam.offset === 0 && pagination.total === 0) return;
  //   setListParam({
  //     ...listParam,
  //     offset: pagination.offset + 1,
  //   });
  // }, [pagination, listParam]);

  useEffect(() => {
    global.aplus.sendPV();
    // if (!global.isInApp()) {
    //   Dialog.confirm({
    //     title: '打开APP',
    //     content: '是否打开小佩宠物APP？',
    //     confirmText: '打开',
    //     cancelText: '取消',
    //     onConfirm: () => {
    //       window.location.href = 'https://file.petkit.cn';
    //     },
    //     onCancel: () => {},
    //   });
    // }
  }, []);

  useEffect(() => {
    if (listParam) requestOrderList(listParam);
  }, [listParam]);

  return (
    // div className="w-full h-full p-4 overflow-scroll"
    <div className="order-list-container">
      <NavBar
        right={
          count >= 3 ? (
            <AntOutline fontSize={20} onClick={global.setupVConsole} />
          ) : (
            ''
          )
        }
        style={{ height: headerHeight }}
        onBack={goBack}
        className={`bg-white order-list-header ${
          global.isiOS() ? 'padding-safe-area-ios' : ''
        } ${global.isAndroid() ? 'padding-safe-area-android' : ''} ${
          global.isHarmony() ? 'padding-safe-area-harmony' : ''
        }`}
      >
        <span
          className="active-color font-bold text-lg"
          onClick={() => setCount(count + 1)}
        >
          订单
        </span>
      </NavBar>
      <div
        className="order-list-body"
        style={{ height: `calc(100% - ${headerHeight}px)` }}
      >
        {orderList && orderList.length ? (
          <>
            <div className="p-4">
              {orderList.map((item, index) => (
                <OrderItem
                  className={`${index !== orderList.length - 1 ? 'mb-4' : ''}`}
                  key={item.id}
                  order={item}
                />
              ))}
            </div>
            <InfiniteScroll
              threshold={10}
              hasMore={pagination.total > pagination.offset + pagination.limit}
              loadMore={loadMore}
            />
          </>
        ) : (
          <ErrorBlock
            image={orderEmptyPng}
            title={<p style={{ fontSize: 12, marginTop: 45 }}>暂无订单</p>}
            description=""
          />
        )}
      </div>
    </div>
  );
};

export default OrderList;
