.container {
}

.header {
  background: transparent;
}

.body {
  padding: 16px;
}

.card {
  background: #fff;
  box-shadow: 0 2px 11px 0 rgba(0, 0, 0, 0.04);
  border-radius: 16px;
  padding: 20px 16px;
}

.title {
  margin-bottom: 14px;

  & > span {
    font-size: 16px;
    font-weight: 600;
    color: #323643;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 4px;
      border-radius: 3px;
      background: #fb7c3d;
      opacity: 0.4;
    }
  }
}

.content {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.video {
  margin-bottom: 20px;
}

.description {
  font-size: 14px;
  color: #666;
  display: flex;
  margin-bottom: 14px;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.image {
  width: 20px;
  height: 20px;
  margin-right: 2px;
}

.divider {
  margin-bottom: 20px;
}
