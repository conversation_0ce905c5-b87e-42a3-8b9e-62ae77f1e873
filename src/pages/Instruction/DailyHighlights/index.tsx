import dailyHeart from '@/assets/daily-heart.png';
import Video from '@/components/Video';
import { useProxyType } from '@/hooks/useProxy';
import { ProxyType, UrlStatusEnum } from '@/models/common.interface';
import { capitalizeFirstLetter } from '@/models/common.util';
import { SuitableDeviceTypeEnum } from '@/models/product/interface';
import global from '@/utils/global';
import useUrlState from '@ahooksjs/use-url-state';
import { useIntl, useParams } from '@umijs/max';
import { Divider, Image, NavBar } from 'antd-mobile';
import React, { useEffect, useMemo, useRef } from 'react';
import styles from './index.less';

interface DeviceContent {
  title: string;
  content: React.ReactNode;
  videoUrl?: string;
}

const DailyHighlights: React.FC = () => {
  const intl = useIntl();
  const [urlParam] = useUrlState<{
    hasNavBar: UrlStatusEnum;
  }>();
  const urlRestParam = useParams<{ deviceType: string }>();
  const proxyType = useProxyType();
  const videoRef = useRef<HTMLVideoElement>(null);
  const title = intl.formatMessage({ id: 'dailyHighlights.instruction.title' });

  const deviceVideoObj: {
    [key in ProxyType]: { [key2 in SuitableDeviceTypeEnum]: string };
  } = {
    online: {
      [SuitableDeviceTypeEnum.D4h]:
        'https://instructions.petkit.com/vlog_d4h.mp4',
      [SuitableDeviceTypeEnum.D4sh]:
        'https://instructions.petkit.com/vlog_d4sh.mp4',
      [SuitableDeviceTypeEnum.T5]:
        'https://instructions.petkit.com/vlog_t5.mp4',
      [SuitableDeviceTypeEnum.T6]:
        'https://instructions.petkit.com/vlog_t6.mp4',
      [SuitableDeviceTypeEnum.T7]: '',
    },
    sandbox: {
      [SuitableDeviceTypeEnum.D4h]:
        'https://instructions.petkit.com/vlog_d4h.mp4',
      [SuitableDeviceTypeEnum.D4sh]:
        'https://instructions.petkit.com/vlog_d4sh.mp4',
      [SuitableDeviceTypeEnum.T5]:
        'https://instructions.petkit.com/vlog_t5.mp4',
      [SuitableDeviceTypeEnum.T6]:
        'https://instructions.petkit.com/vlog_t6.mp4',
      [SuitableDeviceTypeEnum.T7]: '',
    },
  };

  const deviceContentList: DeviceContent[] = useMemo(() => {
    return [
      {
        title: intl.formatMessage({
          id: 'dailyHighlights.instruction.pagragraph1.title',
        }),
        content: intl.formatMessage({
          id: 'dailyHighlights.instruction.pagragraph1.content',
        }),
        videoUrl:
          deviceVideoObj[proxyType][
            capitalizeFirstLetter(
              urlRestParam.deviceType || SuitableDeviceTypeEnum.D4sh,
            )
          ],
      },
      {
        title: intl.formatMessage({
          id: 'dailyHighlights.instruction.pagragraph2.title',
        }),
        content: (
          <>
            <div className={styles.description}>
              <Image className={styles.image} src={dailyHeart} />
              {intl.formatMessage({
                id: 'dailyHighlights.instruction.pagragraph2.content1',
              })}
            </div>
            <div className={styles.description}>
              <Image className={styles.image} src={dailyHeart} />
              {intl.formatMessage({
                id: 'dailyHighlights.instruction.pagragraph2.content2',
              })}
            </div>
            <div className={styles.description}>
              <Image className={styles.image} src={dailyHeart} />
              {intl.formatMessage({
                id: 'dailyHighlights.instruction.pagragraph2.content3',
              })}
            </div>
          </>
        ),
      },
    ];
  }, [urlParam]);

  useEffect(() => {
    document.title = title;
  }, []);

  return (
    <div
      className={`${styles.container} h-full overflow-auto relative ${
        urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isiOS()
          ? 'padding-safe-area-ios'
          : ''
      } ${
        urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isAndroid()
          ? 'padding-safe-area-android'
          : ''
      } ${
        urlParam.hasNavBar === UrlStatusEnum.ENABLE && global.isHarmony()
          ? 'padding-safe-area-harmony'
          : ''
      }`}
    >
      {urlParam.hasNavBar === UrlStatusEnum.ENABLE ? (
        <NavBar onBack={global.closeWebview} className={styles.header}>
          <span className="active-color font-bold text-lg">{title}</span>
        </NavBar>
      ) : null}
      <div className={styles.body}>
        <div className={styles.card}>
          {deviceContentList.map((item, index) => (
            <div key={String(index)}>
              <div className={styles.title}>
                <span>{item.title}</span>
              </div>
              <div className={styles.content}>{item.content}</div>
              {item.videoUrl ? (
                <div className={styles.videoContainer}>
                  <Video
                    ref={videoRef}
                    className={styles.video}
                    src={item.videoUrl}
                    controls
                    onClick={() => {
                      videoRef.current?.play();
                      videoRef.current?.requestFullscreen();
                    }}
                    poster={`${item.videoUrl}?vframe/jpg/offset/2.7`}
                  />
                </div>
              ) : null}
              {index !== deviceContentList.length - 1 ? (
                <Divider className={styles.divider} />
              ) : null}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DailyHighlights;
