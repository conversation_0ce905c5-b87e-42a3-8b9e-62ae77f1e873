import { Key, UrlStatusEnum } from '@/models/common.interface';
import global from '@/utils/global';
import { uuid } from '@/utils/uuid';
import useUrlState from '@ahooksjs/use-url-state';
import { history, useIntl } from '@umijs/max';
import { Image, NavBar } from 'antd-mobile';
import React, { useEffect } from 'react';
import styles from './index.less';

interface UrlParam {
  hasNavBar: UrlStatusEnum;
}

const initUrlParam: UrlParam = {
  hasNavBar: UrlStatusEnum.DISABLE,
};

interface Desc extends Key {
  title: string;
  description: string;
  image: string;
}

const Entrance: React.FC = () => {
  const [urlParam] = useUrlState<{ hasNavBar: string }>(initUrlParam);
  const intl = useIntl();
  const title = intl.formatMessage({ id: 'entrance.title' });
  const descList: Desc[] = [
    {
      key: uuid(),
      title: intl.formatMessage({ id: 'entrance.paramgraph1.title' }),
      description: intl.formatMessage({ id: 'entrance.paramgraph1.content' }),
      image: intl.formatMessage({ id: 'entrance.paramgraph1.image' }),
    },
    {
      key: uuid(),
      title: intl.formatMessage({ id: 'entrance.paramgraph2.title' }),
      description: intl.formatMessage({ id: 'entrance.paramgraph2.content' }),
      image: intl.formatMessage({ id: 'entrance.paramgraph2.image' }),
    },
    {
      key: uuid(),
      title: intl.formatMessage({ id: 'entrance.paramgraph3.title' }),
      description: intl.formatMessage({ id: 'entrance.paramgraph3.content' }),
      image: intl.formatMessage({ id: 'entrance.paramgraph3.image' }),
    },
    {
      key: uuid(),
      title: intl.formatMessage({ id: 'entrance.paramgraph4.title' }),
      description: intl.formatMessage({ id: 'entrance.paramgraph4.content' }),
      image: intl.formatMessage({ id: 'entrance.paramgraph4.image' }),
    },
  ];

  useEffect(() => {
    document.title = title;
  }, []);

  return (
    <div
      className={`${styles.container} h-full overflow-auto relative ${
        global.isiOS() ? 'padding-safe-area-ios' : ''
      } ${global.isAndroid() ? 'padding-safe-area-android' : ''} ${
        global.isHarmony() ? 'padding-safe-area-harmony' : ''
      }`}
    >
      {urlParam.hasNavBar === UrlStatusEnum.ENABLE ? (
        <NavBar onBack={history.back}>
          <span className="active-color font-bold text-lg">{title}</span>
        </NavBar>
      ) : null}
      <div className={styles.list}>
        {descList.map((desc) => (
          <div key={desc.key} className={styles.item}>
            <div className={styles.title} onClick={global.setupVConsole}>
              {desc.title}
            </div>
            <div
              className={styles.content}
              onClick={() => console.log(intl.locale)}
            >
              {desc.description}
            </div>
            <Image className={styles.media} src={desc.image} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Entrance;
