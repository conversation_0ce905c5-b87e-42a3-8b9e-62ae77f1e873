export const getCurrentDomain = () => {
  const { origin } = location;
  const cnOnline = 'api.petkit.com/latest';
  const cnSandbox = 'api-sandbox.petkit.com/6';
  const enOnline = 'api.petkt.com';
  const enSandbox = 'api-sandbox.petkt.com/6';
  let currentDomain = cnSandbox;

  if (origin.includes('petkit')) {
    if (origin.includes('sandbox')) currentDomain = cnSandbox;
    else if (origin.includes('dev')) currentDomain = cnSandbox;
    else currentDomain = cnOnline;
  }

  if (origin.includes('petkt')) {
    if (origin.includes('sandbox')) currentDomain = enSandbox;
    else currentDomain = enOnline;
  }

  // if (origin.includes('local') || origin.includes('192.168')) {
  //   currentDomain = cnSandbox;
  // }

  return currentDomain;
};
