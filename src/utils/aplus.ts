import { SuitableDeviceTypeEnum } from '@/models/product/interface';

let deviceType = SuitableDeviceTypeEnum.D4sh;
const timeout = 500;

export interface AplusOption {
  userId?: string;
  deviceType?: SuitableDeviceTypeEnum;
}

export enum AplusActionEnum {
  SENDPV = 'aplus.sendPV',
  RECORD = 'aplus.record',
  SET_META_INFO = 'aplus.setMetaInfo',
  GET_META_INFO = 'aplus.getMetaInfo',
}

export enum APlusEventTypeEnum {
  CLICK = 'CLK',
}

export enum PointTypeEnum {
  VISIT = '_visit',
  BUYNOW = '_Buynow',
  PAGESTAY = '_PageStay',
  BACK = '_back',
  BUYNOW_CHECKOUT_CONFIRM = '_Buynow_Checkout_Confirm',
  BUYNOW_PAID_RESULT = '_Buynow_paid_result',
}

export enum AplusCheckoutConfirmPayTypeEnum {
  WECHAT = 'Wechat',
  ALIPAY = 'Alipay',
  ALIPAY_AUTO_RENEW = 'Alipay Auto-renew',
}

export enum AplusPayResultTypeEnum {
  SUCCESS = 'success',
  FAIL = 'FAIL',
}

const getBuriedPointCode = (
  _deviceType: SuitableDeviceTypeEnum,
  pointType: PointTypeEnum,
) => {
  const code = `Petkit_${_deviceType}_PETKITCare+${pointType}`;
  return code;
};

export const sendPV = () => {
  const { aplus_queue } = window as any;
  setTimeout(() => {
    aplus_queue.push({
      action: AplusActionEnum.SENDPV,
      arguments: [{ is_auto: false }],
    });
  }, timeout);
};

// 统一埋点
export const buryPoint = (
  eventId: PointTypeEnum,
  eventParams: Partial<{ [key: string]: string | number }> = {},
  eventType = APlusEventTypeEnum.CLICK,
  action = AplusActionEnum.RECORD,
) => {
  const { aplus_queue } = window as any;
  setTimeout(() => {
    aplus_queue.push({
      action,
      arguments: [
        getBuriedPointCode(deviceType, eventId),
        eventType,
        { ...eventParams },
      ],
    });
  }, timeout);
};

export const initAplus = (option: AplusOption) => {
  if (option.userId) {
    const { aplus_queue } = window as any;
    setTimeout(() => {
      aplus_queue.push({
        action: AplusActionEnum.SET_META_INFO,
        arguments: ['_user_id', option.userId],
      });
    }, timeout);
  }
  if (option.deviceType) deviceType = option.deviceType;
};
