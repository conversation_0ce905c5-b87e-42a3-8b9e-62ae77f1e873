import {
  BreadcrumbInfo,
  ReNewEnum,
  SelectOption,
  ValueType,
} from '@/models/common.interface';
import { SuitableDeviceTypeEnum } from '@/models/product/interface';
import { requestClass as request } from '@/utils/request';
import { Location, setLocale } from '@umijs/max';
import { Dialog } from 'antd-mobile';
import VConsole from 'vconsole';
import { StorageService } from './Storage';
import { buryPoint, initAplus, sendPV } from './aplus';

export enum NativeNavigatePageEnum {
  BACK = -1,
  CLOSE_WEBVIEW = 0,
  MY_CLOUD_SERVICE = 1, // 我的云服务
  PETKIT_CUSTOMER_SERVICE = 2, // 联系客服
  CURRENT_DEVICE_SERVICE_PAGE = 3, // 服务管理
  CURRENT_DEVICE_INDEX_PAGE = 4,
  EXCHANGE_CENTER_PAGE = 5,
}

export interface ExtraNativeMessageParam {
  deviceId?: number;
  deviceType?: SuitableDeviceTypeEnum;
  deviceName?: string;
  packageName?: string;
  packageExpirationTime?: number;
  packageRenewType?: ReNewEnum;
  packageExpirationZoneId?: string;
}

export interface NativeMessageParam {
  functionName: 'navigatePage';
  param: ExtraNativeMessageParam & {
    navigateNumber: NativeNavigatePageEnum;
  };
}

class Global {
  static instance: Global;

  DEFAULT_CURRENCY_SYMBOL = '￥';

  baiduHmKey = '';
  iconfontUrl = '';
  // 蓝牙设备列表
  bluetoothDeviceList = [
    'hg',
    'fit',
    'go',
    'k3',
    'aq',
    'aqr',
    'p3',
    'h2',
    'w5',
    'r2',
  ];
  // wifi设备列表
  wifiDeviceList = [
    't3',
    'mate',
    'feeder',
    'cozy',
    'feedermini',
    'aqh1',
    'k2',
    't4',
    't5',
    'd3',
    'd4',
    'd4s',
    'h3',
  ];
  centerDeviceList = ['aqh1', 'd4s', 't4', 't5', 'h3'];

  deviceNames: { [key: string]: string } = {
    hg: '烘干箱',
    h3: '摄像头',
  };

  storageService: StorageService;

  getToken: () => string;

  aplus = {
    init: initAplus,
    buryPoint,
    sendPV,
  };

  constructor() {
    this.storageService = new StorageService();
    this.getToken = () => this.storageService?.getLocalItem('token') || '';

    if (!this.isInApp()) this._showNotAppTips();

    if (typeof Global.instance === 'object') {
      return Global.instance;
    }

    // this.setupVConsole();

    Global.instance = this;
    return this;
  }

  getOptionsByMap(map: Map<ValueType, string>): SelectOption[] {
    const options: SelectOption[] = [];
    map.forEach((value, key) => {
      options.push({
        label: value,
        value: key,
      });
    });
    return options;
  }

  getBreadcrumbInfo(
    oldBreadcrumbInfo: BreadcrumbInfo,
    prefix?: string,
    location?: Location,
  ): BreadcrumbInfo[] {
    const newRoutes = [oldBreadcrumbInfo];
    const listBreadcrumbInfo: BreadcrumbInfo = {
      path: '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${prefix}列表`,
    };
    const detailBreadcrumbInfo: BreadcrumbInfo = {
      path: '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${prefix}详情`,
    };
    const editBreadcrumbInfo: BreadcrumbInfo = {
      path: '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${prefix}编辑`,
    };
    if (location?.pathname.includes('list')) {
      newRoutes.push(listBreadcrumbInfo);
    } else if (location?.pathname.includes('detail')) {
      newRoutes.push(listBreadcrumbInfo);
      newRoutes.push(detailBreadcrumbInfo);
    } else if (location?.pathname.includes('edit')) {
      newRoutes.push(listBreadcrumbInfo);
      newRoutes.push(editBreadcrumbInfo);
    }
    return newRoutes;
  }

  sendMessageToIOS(param: NativeMessageParam) {
    const {
      functionName,
      param: { navigateNumber, ...rest },
    } = param;
    if (!(window as any).webkit || !(window as any).webkit.messageHandlers)
      return;
    if (navigateNumber <= 4) {
      (window as any).webkit.messageHandlers[functionName].postMessage(
        navigateNumber,
      );
      return;
    }
    (window as any).webkit.messageHandlers[functionName].postMessage({
      navigateNumber,
      ...rest,
    });
  }

  sendMessageToAndroid(param: NativeMessageParam) {
    const {
      functionName,
      param: { navigateNumber, ...rest },
    } = param;
    // Toast.show({
    //   content: JSON.stringify(param),
    //   duration: 5000,
    // });
    console.log(navigateNumber, rest);
    if (!(window as any).android) return;
    if (navigateNumber <= 4) {
      (window as any).android[functionName](navigateNumber);
      return;
    }
    (window as any).android[functionName](navigateNumber, JSON.stringify(rest));
  }

  sendMessageToHarmony(param: NativeMessageParam) {
    const {
      functionName,
      param: { navigateNumber, ...rest },
    } = param;
    // Toast.show({
    //   content: JSON.stringify(param),
    //   duration: 5000,
    // });
    console.log(navigateNumber, rest);
    if (!(window as any).harmony) return;
    if (navigateNumber <= 4) {
      (window as any).harmony[functionName](navigateNumber);
      return;
    }
    (window as any).harmony[functionName](navigateNumber, JSON.stringify(rest));
  }

  gotoBackNativePage() {
    const param: NativeMessageParam = {
      functionName: 'navigatePage',
      param: { navigateNumber: NativeNavigatePageEnum.BACK },
    };
    Global.instance.sendMessageToNative(param);
  }

  closeWebview() {
    const param: NativeMessageParam = {
      functionName: 'navigatePage',
      param: { navigateNumber: NativeNavigatePageEnum.CLOSE_WEBVIEW },
    };
    Global.instance.sendMessageToNative(param);
  }

  gotoMyCloudServiceList() {
    const param: NativeMessageParam = {
      functionName: 'navigatePage',
      param: { navigateNumber: NativeNavigatePageEnum.MY_CLOUD_SERVICE },
    };
    Global.instance.sendMessageToNative(param);
  }

  gotoCustomerService() {
    const param: NativeMessageParam = {
      functionName: 'navigatePage',
      param: { navigateNumber: NativeNavigatePageEnum.PETKIT_CUSTOMER_SERVICE },
    };
    Global.instance.sendMessageToNative(param);
  }

  gotoCurrentDeviceServicePageWithCloseWebview() {
    Global.instance.closeWebview();

    Global.instance.gotoCurrentDeviceServicePage();
  }

  gotoCurrentDeviceServicePage() {
    const param: NativeMessageParam = {
      functionName: 'navigatePage',
      param: {
        navigateNumber: NativeNavigatePageEnum.CURRENT_DEVICE_SERVICE_PAGE,
      },
    };
    Global.instance.sendMessageToNative(param);
  }
  gotoCurrentDeviceIndexPage() {
    const param: NativeMessageParam = {
      functionName: 'navigatePage',
      param: {
        navigateNumber: NativeNavigatePageEnum.CURRENT_DEVICE_INDEX_PAGE,
      },
    };
    Global.instance.sendMessageToNative(param);
  }

  // 跳转激活中心
  gotoExchangeCenterPage(extraParam?: ExtraNativeMessageParam) {
    const param: NativeMessageParam = {
      functionName: 'navigatePage',
      param: {
        navigateNumber: NativeNavigatePageEnum.EXCHANGE_CENTER_PAGE,
        ...extraParam,
      },
    };
    Global.instance.sendMessageToNative(param);
  }

  // 判断是否为安卓机
  isAndroid() {
    return this.getBrowserInfo().android;
  }

  isiOS() {
    return this.getBrowserInfo().isiOS;
  }

  // 判断是否是鸿蒙机
  isHarmony() {
    return this.getBrowserInfo().isHarmony;
  }

  isInApp() {
    // return true;
    return (
      ((window as any).webkit &&
        (window as any).webkit.messageHandlers &&
        (window as any).webkit.messageHandlers.navigatePage) ||
      ((window as any).android && (window as any).android.navigatePage) ||
      ((window as any).harmony && (window as any).harmony.navigatePage)
    );
  }

  isOnline() {
    return location.origin.includes('api.petkit');
  }

  setupVConsole() {
    if (
      window.location.origin.includes('sandbox') ||
      window.location.origin.includes('dev') ||
      window.location.origin.includes('local') ||
      window.location.origin.includes('192.168')
    ) {
      const vConsole = new VConsole();

      console.log(vConsole);
    }
  }

  saveToken(token: string) {
    localStorage.setItem('sessionToken', token);
    if (request) request.token = token;
  }

  saveLangauge(language: string | 'null' = 'zh-CN') {
    let origin_langauge = language;
    if (language === 'null') {
      origin_langauge = 'zh-CN';
    }
    let _language = origin_langauge.replace('_', '-');
    setLocale(_language, false);
    localStorage.setItem('language', _language);
  }

  saveEnter(entrance: string) {
    sessionStorage.setItem('entrance', entrance || 'unknown');
  }

  saveUserInfo(userId: string) {
    sessionStorage.setItem('userId', userId || 'unknown');
  }

  // 仅用于埋点统计功能
  saveDeviceInfo(deviceInfo: {
    deviceId?: string;
    deviceType?: SuitableDeviceTypeEnum;
  }) {
    localStorage.setItem('deviceInfo', JSON.stringify(deviceInfo));
  }

  private _showNotAppTips() {
    const { href } = window.location;
    if (
      !href.includes('redirect') &&
      !href.includes('description') &&
      !href.includes('instruction')
    ) {
      Dialog.confirm({
        title: '打开APP',
        content: '是否打开小佩宠物APP？',
        confirmText: '打开',
        cancelText: '取消',
        onConfirm: () => {
          window.location.href = 'https://file.petkit.cn';
        },
        onCancel: () => {},
      });
    }
  }

  private sendMessageToNative(param: NativeMessageParam) {
    Global.instance.sendMessageToIOS(param);
    Global.instance.sendMessageToAndroid(param);
    Global.instance.sendMessageToHarmony(param);
  }

  private getBrowserInfo() {
    const u = navigator.userAgent;
    return {
      trident: u.indexOf('Trident') > -1, // IE内核
      webKit: u.indexOf('AppleWebKit') > -1, // 苹果、谷歌内核
      mobile: !!u.match(/AppleWebKit.*Mobile.*/), // 是否为移动终端
      android: u.indexOf('Android') > -1 || u.indexOf('Adr') > -1, // android终端
      iPad: u.indexOf('iPad') > -1, // 是否iPad
      weixin: u.indexOf('MicroMessenger') > -1, // 是否微信 （2015-01-22新增）
      isiOS: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios终端
      isHarmony: u.indexOf('HarmonyOS') > -1 || u.indexOf('ArkWeb') > -1, // 判断是否是 鸿蒙终端
    };
  }
}

export default new Global();
