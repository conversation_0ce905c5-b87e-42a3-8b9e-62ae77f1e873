import Request, { Result } from '@mantas/request';
import { history } from '@umijs/max';
import { Toast } from 'antd-mobile';
import global from './global';

interface ResponseResult<T> {
  result?: T;
  error?: ResponseError;
}

interface ResponseError {
  code: number;
  msg: string;
}

export interface PaginatorParam {
  limit: number;
  offset: number;
}

export interface Pagination {
  limit: number;
  offset: number;
  total: number;
}

export type Data<T = unknown> = Pagination & {
  items: T[];
};

export const initPaginatorParam: PaginatorParam = {
  limit: 10,
  offset: 0,
};

export const initPagination: Pagination = {
  limit: 10,
  offset: 0,
  total: 0,
};

export let requestClass: Request | null = null;

if (!requestClass) {
  requestClass = new Request({
    token: localStorage.getItem('sessionToken') || '',
    tokenKey: 'X-Session',
    // 不知道为什么，此处的session携带位置必须设置为body or query中才可以实现跨域，否则会报跨域问题
    // tokenPlaces: [TokenPlaceEnum.PARAMS],
    timeout: 50 * 1000,
    customerHeader: {
      'X-Locale': localStorage.getItem('language') || 'zh_CN',
    },
    customResolveResponseResult: (responseData: ResponseResult<any>) => {
      const result: Result<any> = {
        code: -1,
        data: null,
        message: '请求失败！',
      };
      if (
        JSON.stringify(responseData) === '{}' ||
        responseData.result ||
        responseData.result === 0
      ) {
        result.code = 0;
        result.message = '';
        result.data = responseData.result;
        // result.code = -1;
        // result.message = '报错了';
      } else if (responseData.error) {
        result.code = responseData.error.code;
        result.message = responseData.error.msg;
      }
      return result;
    },
    resolveHttpStatusOperation: {
      401: () => {
        const isLoginPage = history.location.pathname === '/login';
        if (!isLoginPage) {
          history.replace('/login');
        }
      },
    },
    onErrorMessage: (errorMsg: string) => {
      if (global.isInApp())
        Toast.show({
          icon: 'fail',
          content: errorMsg,
          duration: (window as any).toastDuration || 2000,
        });

      // 如果接口权限不足，则提示完成后自动关闭webview
      if (errorMsg.includes('权限不足')) {
        setTimeout(() => {
          global.closeWebview();
        }, 2000);
        return;
      }
    },
  });
}

const request = requestClass.getRequestResult();

export default request;
