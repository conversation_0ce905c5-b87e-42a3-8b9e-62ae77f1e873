import { MAX_REST_DAYS } from '@/models/common.util';
import { DeviceInfoWithPackage } from '@/models/device/interface';
import { initDeviceInfoWithPackage } from '@/models/device/util';
import { ProductSku, ServiceTimeUnitEnum } from '@/models/product/interface';
import { initProductSku } from '@/models/product/util';
import {
  getAutoPackageInfoTip,
  getExistedPackageTip,
  getRestDays,
  getValidDataMessage,
  pendingPackageInfoTip,
} from '@/pages/util';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { tranferProductSkuToDevicePackageInfo } from '../util';

const deviceInfo: DeviceInfoWithPackage = {
  ...initDeviceInfoWithPackage,
};

// 非自月 1级 活动天
const nonAutorenew1stLevelDailyPackage: ProductSku = {
  ...initProductSku,
  shortName: '非自月 1级',
  serviceTimeUnit: ServiceTimeUnitEnum.DAY,
  level: 1,
};

// 非自月 1级
const nonAutorenew1stLevelMonthlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '非自月 1级',
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  level: 1,
};
// 非自月 2级
const nonAutorenew2stLevelMonthlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '非自月 2级',
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  level: 2,
};

// 自月 1级
const autoRenew1stLevelMonthlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '自月 1级',
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  level: 1,
  price: {
    ...initProductSku.price,
    isReNew: true,
  },
};
// 自月 2级
const autoRenew2stLevelMonthlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '自月 2级',
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  level: 2,
  price: {
    ...initProductSku.price,
    isReNew: true,
  },
};

// 非自年 1级
const nonAutoRenew1stLevelYearlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '非自年 1级',
  serviceTimeUnit: ServiceTimeUnitEnum.YEAR,
  level: 1,
};
// 非自年 2级
const nonAutoRenew2stLevelYearlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '非自年 2级',
  serviceTimeUnit: ServiceTimeUnitEnum.YEAR,
  level: 2,
};

// 自年 1级
const autoRenew1stLevelYearlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '自年 1级',
  serviceTimeUnit: ServiceTimeUnitEnum.YEAR,
  level: 1,
  price: {
    ...initProductSku.price,
    isReNew: true,
  },
};
// 自年 2级
const autoRenew2stLevelYearlyPackage: ProductSku = {
  ...initProductSku,
  shortName: '自年 2级',
  serviceTimeUnit: ServiceTimeUnitEnum.YEAR,
  level: 2,
  price: {
    ...initProductSku.price,
    isReNew: true,
  },
};

// 活动 2级非自月套餐，活动时长大于30天
const nonAutoRenew60DaysPackage: ProductSku = {
  ...initProductSku,
  shortName: '非自年 2级',
  serviceTimeUnit: ServiceTimeUnitEnum.DAY,
  serviceTime: 60,
  level: 2,
};

beforeAll(() => {
  dayjs.extend(duration);
});

describe('pages/util.tsx测试', () => {
  it('当前生效订阅套餐时，不可购买/订阅任意服务 1级自动月 -> 1级非自月 平级', () => {
    // 设备信息中要有订阅套餐
    const _deviceInfo: DeviceInfoWithPackage = {
      ...deviceInfo,
      effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
        autoRenew1stLevelMonthlyPackage,
      ),
    };

    expect(
      getValidDataMessage(_deviceInfo, nonAutorenew1stLevelMonthlyPackage),
    ).toEqual(getAutoPackageInfoTip(autoRenew1stLevelMonthlyPackage.name));
  });

  it('存在pending服务时，订阅任意服务时 1级非自月 -> 1级自月 平级', () => {
    const expectElement = pendingPackageInfoTip;
    // 设备信息中要有订阅套餐
    const _deviceInfo: DeviceInfoWithPackage = {
      ...deviceInfo,
      effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
        nonAutorenew1stLevelMonthlyPackage,
      ),
      pendingProductInfos: [
        tranferProductSkuToDevicePackageInfo(
          nonAutorenew1stLevelMonthlyPackage,
        ),
      ],
    };

    expect(
      getValidDataMessage(_deviceInfo, autoRenew1stLevelMonthlyPackage),
    ).toEqual(expectElement);
  });

  it('当前生效的服务剩余周期大于30天时，购买自动续费套餐，是否提示无法升级，1级非自月 -> 1级自月 平级', () => {
    const expectElement = getExistedPackageTip(
      tranferProductSkuToDevicePackageInfo(nonAutoRenew1stLevelYearlyPackage),
    );
    // 设备信息中要有订阅套餐
    const _deviceInfo: DeviceInfoWithPackage = {
      ...deviceInfo,
      effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
        nonAutoRenew1stLevelYearlyPackage,
      ),
    };

    expect(
      getValidDataMessage(_deviceInfo, autoRenew1stLevelMonthlyPackage),
    ).toEqual(expectElement);
  });

  it('当前生效的服务剩余周期大于30天时，购买自动续费套餐，是否提示无法升级，2级非自月 -> 1级自月 升级', () => {
    const expectElement = getExistedPackageTip(
      tranferProductSkuToDevicePackageInfo(nonAutoRenew60DaysPackage),
    );
    // 设备信息中要有订阅套餐
    const _deviceInfo: DeviceInfoWithPackage = {
      ...deviceInfo,
      effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
        nonAutoRenew60DaysPackage,
      ),
    };

    expect(
      getValidDataMessage(_deviceInfo, autoRenew1stLevelMonthlyPackage),
    ).toEqual(expectElement);
  });

  it('正常升级or购买 2级非自年 -> 1级非自月 升级', () => {
    const _deviceInfo: DeviceInfoWithPackage = {
      ...deviceInfo,
      effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
        nonAutoRenew2stLevelYearlyPackage,
      ),
    };
    expect(
      getValidDataMessage(_deviceInfo, nonAutorenew1stLevelMonthlyPackage),
    ).toBe('');
  });
});

it('正常非自降级购买非自 1级非自月 -> 2级非自年 降级', () => {
  const _deviceInfo: DeviceInfoWithPackage = {
    ...deviceInfo,
    effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
      nonAutorenew1stLevelMonthlyPackage,
    ),
  };
  expect(
    getValidDataMessage(_deviceInfo, nonAutoRenew2stLevelYearlyPackage),
  ).toBe('');
});

it('正常非自降级购买非自，同时存在pending服务 1级非自月，活动天 -> 2级非自月 降级', () => {
  const _deviceInfo: DeviceInfoWithPackage = {
    ...deviceInfo,
    effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
      nonAutorenew1stLevelDailyPackage,
    ),
    pendingProductInfos: [
      tranferProductSkuToDevicePackageInfo(nonAutorenew1stLevelMonthlyPackage),
    ],
  };
  expect(
    getValidDataMessage(_deviceInfo, nonAutorenew2stLevelMonthlyPackage),
  ).toBe('');
});

it('正常非自降级购买非自，同时存在订阅的pending服务 1级非自月，活动天 -> 2级非自月 降级', () => {
  const _deviceInfo: DeviceInfoWithPackage = {
    ...deviceInfo,
    effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
      nonAutorenew1stLevelDailyPackage,
    ),
    pendingProductInfos: [
      tranferProductSkuToDevicePackageInfo(autoRenew1stLevelMonthlyPackage),
    ],
  };
  expect(
    getValidDataMessage(_deviceInfo, nonAutorenew2stLevelMonthlyPackage),
  ).toEqual(getAutoPackageInfoTip(autoRenew1stLevelMonthlyPackage.name));
});

it('正常非自升级购买非自，同时存在订阅的pending服务 2级非自月 -> 1级非自月 降级', () => {
  const _deviceInfo: DeviceInfoWithPackage = {
    ...deviceInfo,
    effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
      nonAutorenew2stLevelMonthlyPackage,
    ),
    pendingProductInfos: [
      tranferProductSkuToDevicePackageInfo(autoRenew1stLevelMonthlyPackage),
    ],
  };
  expect(
    getValidDataMessage(_deviceInfo, nonAutorenew1stLevelMonthlyPackage),
  ).toEqual(getAutoPackageInfoTip(autoRenew1stLevelMonthlyPackage.name));
});

it('正常非自降级购买自动 1级非自月 -> 2级自月 降级', () => {
  const _deviceInfo: DeviceInfoWithPackage = {
    ...deviceInfo,
    effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
      nonAutorenew1stLevelMonthlyPackage,
    ),
  };
  expect(
    getValidDataMessage(_deviceInfo, autoRenew2stLevelMonthlyPackage),
  ).toBe('');
});

it('正常平级购买 1级非自月 -> 1级非自年 平级', () => {
  const _deviceInfo: DeviceInfoWithPackage = {
    ...deviceInfo,
    effectiveProductInfo: tranferProductSkuToDevicePackageInfo(
      nonAutorenew1stLevelMonthlyPackage,
    ),
  };
  expect(
    getValidDataMessage(_deviceInfo, nonAutoRenew2stLevelYearlyPackage),
  ).toBe('');
});

it('计算两个时间点的天数', () => {
  const startTime = dayjs(1695780674000);
  const endTime = dayjs(1704124799000);

  expect(
    getRestDays({ start: startTime, end: endTime }),
  ).toBeGreaterThanOrEqual(MAX_REST_DAYS);
});
