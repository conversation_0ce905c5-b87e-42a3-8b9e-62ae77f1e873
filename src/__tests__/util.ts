import {
  DevicePackageInfo,
  DeviceServiceProductStatusEnum,
} from '@/models/device/interface';
import { ProductSku } from '@/models/product/interface';
import { getEndTimeByServiceTimeInfo } from '@/pages/util';
import dayjs from 'dayjs';

export const tranferProductSkuToDevicePackageInfo = (
  productSku: ProductSku,
): DevicePackageInfo => {
  const packageInfo: DevicePackageInfo = {
    id: 1,
    serviceId: 1,
    skuId: productSku.id,
    skuName: productSku.name,
    skuShortName: productSku.shortName,
    skuLevel: productSku.level,
    /**
     * @description 生效时间戳
     */
    workTime: dayjs().valueOf(),
    /**
     * @description 到期时间戳
     */
    workIndate: getEndTimeByServiceTimeInfo(
      productSku.serviceTime,
      productSku.serviceTimeUnit,
      dayjs(),
    ).valueOf(),
    subscribe: +productSku.price.isReNew,
    productStatus: DeviceServiceProductStatusEnum.EFFECTIVE,
    // 周期数
    serviceTime: productSku.serviceTime,
    // 周期单位
    serviceTimeUnit: productSku.serviceTimeUnit,
  };
  return packageInfo;
};
