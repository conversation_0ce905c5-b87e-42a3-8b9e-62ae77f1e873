import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { SuitableDeviceTypeEnum } from './models/product/interface';
import global from './utils/global';

dayjs.extend(duration);
dayjs.extend(utc);
dayjs.extend(timezone);

const initAplus = () => {
  const urlSearchParam = new URLSearchParams(location.search);
  const userId =
    urlSearchParam.get('userId') ||
    sessionStorage.getItem('userId') ||
    'unknown';
  let deviceType = urlSearchParam.get('deviceType') as SuitableDeviceTypeEnum;
  if (!deviceType) {
    const deviceInfo = localStorage.getItem('deviceInfo');
    deviceType = deviceInfo ? JSON.parse(deviceInfo).deviceType : '';
  }
  global.aplus.init({ userId, deviceType });
};

const run = () => {
  initAplus();
};

run();
