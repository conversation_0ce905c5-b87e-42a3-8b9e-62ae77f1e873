import pic1 from '@/assets/entrance/zh/pic1.gif';
import pic2 from '@/assets/entrance/zh/pic2.gif';
import pic3 from '@/assets/entrance/zh/pic3.gif';
import {
  default as pic4,
  default as pic5,
  default as pic6,
} from '@/assets/entrance/zh/pic4.gif';

export default {
  // 每日精彩介绍页
  'dailyHighlights.instruction.title': '每日精彩',
  'dailyHighlights.instruction.pagragraph1.title': '功能介绍',
  'dailyHighlights.instruction.pagragraph1.content':
    '自动抓取宠物的精彩动态，每天8点后可将昨日的宠物动态制作成1个短视频，回顾宠物一天的有趣瞬间。',
  'dailyHighlights.instruction.pagragraph2.title': '每日精彩里您可以',
  'dailyHighlights.instruction.pagragraph2.content1':
    '捕捉宠物一天的精彩浓缩于眼前',
  'dailyHighlights.instruction.pagragraph2.content2': '捕捉宠物每一个瞬间',
  'dailyHighlights.instruction.pagragraph2.content3': '向好友分享宠物的动态',
  // 全局
  // expirationTitle: 'Term of validity',
  // noData: '暂无数据',
  // noCloudPlans: 'There are currently no plans available',
  // openAppTitle: '打开APP',
  // openAppContent: '是否打开小佩宠物APP？',
  // openAppConfirm: '打开',
  // openAppCancel: '取消',
  // payNow: '立即支付',
  // and: ' and ',
  // loading: '加载中...',
  // 'payConfirm.content': '您是否已完成支付？',
  // 'payConfirm.done': '已完成支付',
  // 'payConfirm.not': '支付碰到问题',
  // 'subConfirm.content': '您是否已完成签约？',
  // 'subConfirm.done': '已完成签约',
  // 'subConfirm.not': '签约碰到问题',
  // 'offLine.tip': '网络连接断开，请检查网络设置~',
  // 'subscripting.tip': '该设备处于订阅中，无法重复购买~',
  // 'unknownDevice.tip': '查无此设备，请检查是否选择了H3设备~',
  // 'motion.record': 'Motion',
  // 'motion.record.subtitle': 'The camera will capture movement',
  // 'motion.record.content': "The camera will capture movement so you don't miss any exciting action",
  // 'cvr.record': 'CVR',
  // 'cvr.record.subtitle': 'Continuous recording 24 hours a day',
  // 'cvr.record.content': 'Continuous recording 24 hours a day, without missing a second',
  // 'basic.record': 'Basic',
  // 'basic.record.subtitle': 'Dynamic editing made videos more fascinating',
  // 'basic.record.content':
  //   'Time-lapse video, daily memories: capture pets interesting moments, and condense the highlights in front of your eyes',
  // 'year.title': 'YEARLY',
  // 'season.title': 'SEASONLY',
  // 'month.title': 'MONTHLY',
  // 'day.title': 'DAILY',
  // 'year.name': 'year',
  // 'season.name': 'season',
  // 'month.name': 'month',
  // 'day.name': 'day',
  // 'year.package': 'yearly package',
  // 'season.package': 'seasonly package',
  // 'month.package': 'monthly package',
  // 'day.package': 'dayly package',
  // selector页面
  'package.selector.instruction.title': '服务说明',
  'package.selector.title': 'Petkit Care+',
  'package.selector.device.subtitle.default': '未开通 PETKIT Care+ 服务',
  'package.selector.device.service.status.waiting': '待生效',
  'package.selector.device.service.status.waiting.lowerCase': '待生效',
  'package.selector.device.service.expiration.title': '有效期：',
  'package.selector.device.pending.service.expiration.title': '有效期至',
  'package.selector.subDisabledTip': '签约已解约!',
  'package.selector.subCanceledTip': '签约已取消!',
  'package.selector.subErrorTip': '遇到错误，请重新发起签约。',
  'package.selector.purchase.fail1.1': '您当前还有“',
  'package.selector.purchase.fail1.2': '”的套餐，暂时无法购买此套餐',
  'package.selector.purchase.fail2.1': '当前设备已拥有“',
  'package.selector.purchase.fail2.2': '”套餐，更换套餐请先取消自动续费',
  'package.selector.purchase.fail3.1': '当前设备已拥有“',
  'package.selector.purchase.fail3.2': '”套餐，',
  'package.selector.purchase.fail3.3': '有效期至',
  'package.selector.purchase.fail3.4': '，暂时无法购买此套餐',
  // sku权益信息
  'package.selector.benefit.title': '专属特权',
  'package.selector.benefit.more': '更多特权',
  'package.selector.benefit.table.free': '免费',
  'package.selector.benefit.table.price': '价格',
  'package.selector.benefit.table.image': '事件图像',
  'package.selector.benefit.table.highlight': '动态视频回看',
  'package.selector.benefit.table.1080P': '1080P 完整视频回看',
  'package.selector.benefit.table.vlog': '每日精彩',
  'package.selector.benefit.table.displayTheme': '专属装扮',
  'package.selector.benefit.table.one.day.loop': '1天循环',
  'package.selector.benefit.table.seven.day.loop': '7天循环',
  'package.selector.benefit.table.thirty.day.loop': '30天循环',
  'package.selector.benefit.table.support': '支持',
  'package.selector.benefit.table.fecesPic': '便便图',
  'package.selector.benefit.table.fullPlayback': '完整视频回看',
  'package.selector.benefit.table.fullPlayback.free': '当天30s标清回看',
  'package.selector.benefit.table.fullPlayback.basic': '全时长高清回看 1天循环',
  'package.selector.benefit.table.fullPlayback.premium':
    '全时长高清回看 7天循环',
  'package.selector.benefit.table.fullPlayback.premiumPlus':
    '全时长高清回看 30天循环',
  'package.selector.benefit.table.healthyAnalysis': '健康分析',
  // 开通按钮
  'package.selector.buttonText.activity':
    '{priceSymbol}{price}试用{number}{unit}',
  'package.selector.buttonText.activity.free':
    '{priceSymbol}{price}试用{number}{unit}',
  // 右上角按钮
  'package.selector.exchange.button': '激活中心',
  'package.selector.exchange.content': '详情请咨询当地销售方',
  'package.selector.exchange.error.noDevice':
    '当前没有设备信息，请先确认设备信息再进行激活操作!',
  'package.selector.myOrder.button': '我的订单',

  'package.selector.activity.duration': '活动时间：',
  'package.selector.normal.buttonText': '立即开通',
  'package.selector.normal.buttonTip': '到期自动续费，可随时取消',
  // confirm页面
  'package.selector.confirm.title': '订单结算',
  'package.selector.confirm.renewDes': '{nextTime}自动续费，可随时关闭',
  'package.selector.confirm.payPrice': '支付金额',
  'package.selector.confirm.restPayTip': '剩余支付时间',
  'package.selector.confirm.paying': '立即支付',
  // confirm页面中的packageInfo
  'package.selector.confirm.packageInfo.name.title': '套餐名称: ',
  'package.selector.confirm.packageInfo.expiration.title': '套餐有效期',
  'package.selector.confirm.packageInfo.orderType.title': '订单类型: ',
  'package.selector.confirm.packageInfo.orderType.text': '{number}{unit}',
  // order页面
  'order.title': '订单',
  'order.endTip': '已经到底啦~',
  'order.item.payPrice': '支付金额',
  'order.item.cancel': '取消订单',
  'order.item.cancelSuccess​': '订单取消成功',
  'order.noData': '暂无订单',
  'order.status.unkown': '未知状态',
  'order.status.created': '已创建',
  'order.status.waitDeduct': '待扣款',
  'order.status.paying': '待支付',
  'order.status.paySucceed': '付款成功',
  'order.status.payFailed': '付款失败',
  'order.status.refundFailed': '退款失败',
  'order.status.cancelled': '已取消',
  'order.status.cancelledSystem': '已取消',
  'order.status.finished': '已完结',
  'order.info.no.title': '订单编号：',
  'order.info.payTime.title': '下单时间：',
  'order.info.payType.title': '支付方式：',
  'order.info.isRenew.title': '自动续费',
  'order.pay.type.free': '活动领取',
  'order.pay.type.exchangeCoupon': '卡券激活',

  // payComp组件内容
  'payComp.order.cancel': '订单已生成，支付被取消',
  'payComp.order.error': '遇到错误，请重新发起支付。',
  'payComp.pay.error.package': '请先选择服务套餐',
  'payComp.pay.error.valid.device.notExist': '当前不存在的合法的设备',
  'payComp.pay.error.device.invalid': '当前设备不合法',

  // 结算页面
  'result.title': '订单结算',
  'result.loading': '正在获取支付结果...',
  'result.success.title': '支付成功',
  'result.success.button': '查看我的云服务',
  'result.fail.title': '支付失败',
  'result.fail.reason.title': '失败原因：',
  'result.fail.reason.content': '请检查网络是否正常',
  'result.fail.button.customerService': '联系客服',
  'result.fail.button.repay': '继续支付',
  'result.success.title.subscription': '签约成功',
  'result.success.button.subscription': '查看我的云服务',
  'result.process.title.subscription': '请完成签约流程',
  'result.fail.title.subscription': '签约失败',

  // 领取成功页面
  'receive.success.title': '领取成功',
  'receive.success.button.serviceDetail': '查看服务详情',
  'receive.success.button.start': '开始体验',

  // 升级购买确认页
  'mention.title': '开通提醒',
  'mention.current.package.title': '当前套餐',
  'mention.current.package.info.paragraph.1': '当前设备已拥有“',
  'mention.current.package.info.paragraph.2': '”（',
  'mention.current.package.info.paragraph.3': '有效期',
  'mention.current.package.info.paragraph.4': '）。',
  'mention.current.package.with.pending.info.paragraph.1': '当前设备已拥有“',
  'mention.current.package.with.pending.info.paragraph.2': '多个套餐',
  'mention.current.package.with.pending.info.paragraph.3': '”，（确定要开通“',
  'mention.current.package.with.pending.info.paragraph.4': '”）。',
  'mention.next.package.with.pending.info.paragraph.1':
    '开通成功后，将在所有套餐到期后，',
  'mention.next.package.with.pending.info.paragraph.2': ' ',
  'mention.next.package.with.pending.info.paragraph.3': '（',
  'mention.next.package.with.pending.info.paragraph.4': '）',
  'mention.next.package.with.pending.info.paragraph.5': '生效，新套餐有效期至',
  'mention.next.package.with.pending.info.paragraph.6': '。',
  'mention.next.package.info.paragraph.1': '更换“',
  'mention.next.package.info.paragraph.2': '”，将在原套餐到期后（',
  'mention.next.package.info.paragraph.3': '）生效，新套餐有效期至',
  'mention.next.package.info.paragraph.4': '。',
  'mention.next.package.info.paragraph.4.sign': '，到期后自动续费。',
  'mention.purhcase.title.button.loading': '购买中...',
  'mention.purhcase.title.button': '立即以{currencySymbol}{purchaseAmount}开通',
  'mention.purhcase.title.button.free': '免费开通',
  'mention.purhcase.title.button.sign': '到期自动续费，可随时取消',
  'mention.activation.title': '请选择开通方式',
  'mention.activation.upgrade.card.title.1':
    '支付{currencySymbol}{price}升级为',
  'mention.activation.upgrade.card.title.1.free':
    '支付{currencySymbol}{price}升级为',
  'mention.activation.upgrade.card.title.2': '“',
  'mention.activation.upgrade.card.title.3': '”',
  'mention.activation.upgrade.card.title.4': ' ',
  'mention.activation.upgrade.card.title.4.free': ' ',
  'mention.activation.upgrade.card.subtitle': '升级价格说明',
  'mention.activation.upgrade.card.content':
    '升级套餐价格 = 新套餐价格 - 原套餐单日价格 * 原套餐剩余使用时长',
  'mention.activation.upgrade.card.paragraph.1':
    '*升级成功后，新套餐立即生效，有效期至{date}',
  'mention.activation.upgrade.card.paragraph.2': '。',
  'mention.activation.upgrade.card.paragraph.2.sign':
    '，到期按{periodPrice}自动续费。',
  'mention.activation.upgrade.card.button': '{currencySymbol}{price} 升级套餐',
  'mention.activation.upgrade.card.button.free':
    '{currencySymbol}{price} 升级套餐',
  'mention.activation.purchase.card.title.1': '直接购买',
  'mention.activation.purchase.card.title.2': '“{name}”',
  'mention.activation.purchase.card.title.3': '套餐',
  'mentin.activation.purchase.card.expiration.title':
    '*购买成功后，新套餐将在原套餐到期后（{effectiveTime}）生效。',
  'mentin.activation.purchase.card.button.title': '直接购买套餐',
  'mention.activation.sign.card.title.1': '直接购买',
  'mention.activation.sign.card.title.2': '“{name}”',
  'mention.activation.sign.card.title.3': '套餐',
  'mentin.activation.sign.card.expiration.title':
    '*购买成功后，新套餐将在原套餐到期后（{start}）生效。有效期至{end}，到期自动续费。',
  'mentin.activation.sign.card.button.title': '直接购买套餐',

  // description页面
  // 云服务介绍内容
  'description.introduction.title': 'PETKIT Care+',
  'description.introduction.paragraph1.title': 'PETKIT Care+',
  'description.introduction.paragraph1.content1': '智能 守护 陪伴',
  'description.introduction.paragraph1.content2': '全天候AI守护者',
  'description.introduction.paragraph1.image': pic1,
  'description.introduction.paragraph2.title': '云端回溯',
  'description.introduction.paragraph2.content':
    '持续记录宠物的完整活动，云端加密存储，一人购买，全家共享',
  'description.introduction.paragraph2.image': pic2,
  'description.introduction.paragraph3.title': '智能通知',
  'description.introduction.paragraph3.content':
    '实时将专属可视设备的宠物活动事件推送通知，可立即查看高清图像。',
  'description.introduction.paragraph3.image': pic3,
  'description.introduction.paragraph4.title': 'AI HealthKit',
  'description.introduction.paragraph4.content':
    '可视猫厕所专属AI分析，精准捕捉如厕时叫声，同步检测尿液酸碱度，有异常即时提醒。',
  'description.introduction.paragraph4.image': pic4,
  'description.introduction.paragraph5.title': '高清VLOG',
  'description.introduction.paragraph5.content':
    '将每日宠物动态制作成短视频，回顾宠物一天的有趣瞬间。',
  'description.introduction.paragraph5.image': pic5,
  'description.introduction.paragraph6.title': '升级画质',
  'description.introduction.paragraph6.content':
    '独享增强1080P高清流媒体画质，细节清晰可见。',
  'description.introduction.paragraph6.image': pic6,
  // 云服务说明协议
  'description.protocol.title': 'PETKIT Care+ 服务说明协议',
  'description.protocol.url':
    '//file5.petkit.cn/matefw/2024/1/9/659cbf679cbcf1000cc41699QCWmOLcxq',
  // 自动续费服务协议
  'description.autoRenewProtocol.title': 'PETKIT Care+自动续费服务协议',
  'description.autoRenewProtocol.url':
    '//file5.petkit.cn/hg-fw/2024/2/28/65deaa277029f2000ccd2d52WaTWeu9C2',
  // bs-1.16 - 本地化支付
  'order.pay.type.alipayPlus': 'Alipay+',
  'package.selector.sku.description': '每{unit}仅需{symbol}{price}',
  // 多设备计划模块
  'multipleDevicePlan.card.title': '拥有多台PETKIT设备？',
  'multipleDevicePlan.card.subtitle': '多设备套餐',
  'multipleDevicePlan.card.description': '一个订阅，覆盖所有PETKIT Care+设备',
  'multipleDevicePlan.card.price': '起价',
  'multipleDevicePlan.page.title': '多设备·省心计划',
  'multipleDevicePlan.page.mainTitle': '拥有多台PETKIT设备？',
  'multipleDevicePlan.page.mainSubtitle': '多设备套餐',
  'multipleDevicePlan.page.mainDescription':
    '一个订阅，覆盖所有PETKIT Care+设备',
  'multipleDevicePlan.page.mainPrice': '起价',
  'multipleDevicePlan.page.features.title': '套餐优势',
  'multipleDevicePlan.page.features.item1': '一个订阅管理多台设备，便捷高效',
  'multipleDevicePlan.page.features.item2': '享受更优惠的价格，节省更多成本',
  'multipleDevicePlan.page.features.item3': '所有设备同享高级功能和服务',
  'multipleDevicePlan.page.pricing.title': '价格对比',
  'multipleDevicePlan.page.pricing.single': '单设备套餐',
  'multipleDevicePlan.page.pricing.double': '双设备套餐',
  'multipleDevicePlan.page.pricing.multi': '多设备套餐（买二赠一）',
  'multipleDevicePlan.page.pricing.month': '月',
  'multipleDevicePlan.page.button.select': '立即选择',
  'multipleDevicePlan.page.button.learnMore': '了解更多',
  'multipleDevicePlan.page.disclaimer':
    '具体价格以实际购买页面为准，活动优惠政策可能有时间限制',
};
