import { fetchOrderCreationV2, fetchOrderRepay } from '@/models/order/fetch';
import {
  Order,
  OrderCreationResult,
  OrderCreationV2Param,
  PayTypeEnum,
} from '@/models/order/interface';
import { ProductSku, SuitableDeviceTypeEnum } from '@/models/product/interface';
import { AplusCheckoutConfirmPayTypeEnum, PointTypeEnum } from '@/utils/aplus';
import global from '@/utils/global';
import { Button, Toast } from 'antd-mobile';
import React, { useCallback, useEffect, useState } from 'react';
// import { AplusCheckoutConfirmPayTypeEnum, PointTypeEnum } from '@/utils/aplus';
// import global from '@/utils/global';
import classNames from 'classnames';
import ReactHtmlParser from 'react-html-parser';

interface Props {
  // 第几次支付：首次，非首次
  payTimes: 'first' | 'notFirst';
  // 首次支付用到的参数
  deviceId?: number;
  deviceType?: SuitableDeviceTypeEnum;
  productSku?: ProductSku;
  beforeCreatePayFunction?: (
    selectedProductSku: ProductSku,
    paymentMode: PayTypeEnum,
  ) => boolean;
  createPaySuccessFunction?: (orderId: string, payType: PayTypeEnum) => void;
  // 非首次支付用到的参数
  order?: Order;
  // 是否可以补差价
  upgrade?: boolean;
  // 货币单位
  // currencyCode: string;
}

const PayComp: React.FC<Props> = ({
  deviceId,
  deviceType,
  productSku,
  beforeCreatePayFunction,
  createPaySuccessFunction,
  payTimes,
  order,
  upgrade = false,
}: // currencyCode,
Props) => {
  const [formStrValue, setFormStrValue] = useState('');
  const [isPaying, setIsPaying] = useState(false);

  useEffect(() => {
    if (formStrValue) {
      document.forms[0].submit();
    }
  }, [formStrValue]);

  const payFlowLogic = (
    paymentMode: PayTypeEnum,
    payResult: OrderCreationResult,
  ) => {
    if (!deviceId || !deviceType) return;
    let _url = '';
    const orderCreationResult: OrderCreationResult = {
      ...payResult,
      deviceId: deviceId,
      deviceType: deviceType,
    };
    switch (paymentMode) {
      case PayTypeEnum.ALIPAY:
        setFormStrValue(payResult.payload);
        if (createPaySuccessFunction)
          createPaySuccessFunction(payResult.orderId, paymentMode);
        sessionStorage.setItem(
          'payResultInfo',
          JSON.stringify(orderCreationResult),
        );
        // 记录一下支付/签约信息
        break;
      case PayTypeEnum.WEIXIN: {
        if (createPaySuccessFunction)
          createPaySuccessFunction(payResult.orderId, paymentMode);
        sessionStorage.setItem(
          'payResultInfo',
          JSON.stringify(orderCreationResult),
        );
        _url = payResult.payload;
        if (document.forms[0]) {
          document.forms[0].method = 'post';
          document.forms[0].action = _url;
          document.forms[0].submit();
        }
        break;
      }
      default:
        break;
    }
  };

  const canPay = useCallback(
    (paymentMode: PayTypeEnum) => {
      // 重新发起支付，则直接返回true
      if (payTimes === 'notFirst') return true;
      if (!productSku) return false;
      // 没有提供beforeCreatePayFunction方法时，直接算可以进行支付
      if (!beforeCreatePayFunction) return true;

      return beforeCreatePayFunction(productSku, paymentMode);
    },
    [productSku],
  );

  const confirmToPay = useCallback(
    async (paymentMode: PayTypeEnum) => {
      if (isPaying) return;

      if (!productSku) {
        Toast.show(`请先选择服务套餐`);
        return;
      }

      if (!deviceId) {
        Toast.show(`当前不存在的合法的设备`);
        return;
      }

      if (!deviceType) {
        Toast.show(`当前设备不合法`);
        return;
      }

      if (!canPay(paymentMode)) return;

      setIsPaying(true);

      const param: OrderCreationV2Param = {
        deviceId,
        deviceType,
        skuId: productSku.id,
        platform: paymentMode,
        upgrade: +upgrade,
      };
      if (productSku.actPackage && productSku.actPackage.actPackageId) {
        param.activityId = productSku.actPackage.actPackageId;
      }

      global.aplus.buryPoint(PointTypeEnum.BUYNOW_CHECKOUT_CONFIRM, {
        method:
          paymentMode === PayTypeEnum.ALIPAY
            ? AplusCheckoutConfirmPayTypeEnum.ALIPAY
            : AplusCheckoutConfirmPayTypeEnum.WECHAT,
      });

      try {
        // const payResult = await fetchOrderCreation(param);
        const payResult = await fetchOrderCreationV2(param);
        payFlowLogic(paymentMode, payResult);
      } catch (err: any) {
        Toast.show({
          content: err.message || '领取失败',
          icon: 'fail',
        });
      } finally {
        setIsPaying(false);
      }
    },
    [productSku],
  );

  // 重新发起支付
  const payAgain = useCallback(async () => {
    if (!order) return;
    console.log(canPay(order.platform));
    if (!canPay(order.platform)) return;

    const repayResult = await fetchOrderRepay(order.orderId);
    try {
      payFlowLogic(order.platform, repayResult);
    } catch (err) {
      console.log(err);
    } finally {
      setIsPaying(false);
    }
  }, [order]);

  return (
    <>
      {payTimes === 'first' ? (
        <div className="flex justify-center items-center flex-1">
          <Button
            className={classNames('flex-grow')}
            shape="rounded"
            style={{
              backgroundColor: '#027aff',
              color: '#fff',
              marginRight: 15,
            }}
            onClick={() => confirmToPay(PayTypeEnum.ALIPAY)}
            loading={isPaying}
            disabled={isPaying}
          >
            支付宝支付
          </Button>
          <Button
            className="flex-grow"
            shape="rounded"
            style={{ backgroundColor: '#6bc839', color: '#fff' }}
            onClick={() => confirmToPay(PayTypeEnum.WEIXIN)}
            loading={isPaying}
            disabled={isPaying}
          >
            微信支付
          </Button>
        </div>
      ) : (
        <Button
          disabled={isPaying}
          loading={isPaying}
          className="pl-4 pr-4 pt-1 pb-1"
          shape="rounded"
          color="primary"
          fill="outline"
          onClick={payAgain}
        >
          付款
        </Button>
      )}
      <div style={{ display: 'none' }}>{ReactHtmlParser(formStrValue)}</div>
      <form style={{ display: 'none' }} />
    </>
  );
};

export default PayComp;
