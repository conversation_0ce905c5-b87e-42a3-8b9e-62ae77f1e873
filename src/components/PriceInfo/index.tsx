import global from '@/utils/global';
import React from 'react';

interface Props {
  price: number;
  serviceTimeUnit?: string;
  symbol?: string;
}

const PriceInfo: React.FC<Props> = ({
  price,
  serviceTimeUnit = '',
  symbol = global.DEFAULT_CURRENCY_SYMBOL,
}: Props) => {
  return (
    <>
      {serviceTimeUnit ? `每${serviceTimeUnit}仅需` : ''}
      {symbol}
      {price}
    </>
  );
};

export default PriceInfo;
