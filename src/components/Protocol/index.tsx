import React from 'react';
import { RightOutline } from 'antd-mobile-icons';
import { DescriptionTypeEnum } from '@/pages/CloudService/interface';
import global from '@/utils/global';
import { history } from '@umijs/max';

interface ProtocolProps {
  onNavigate?: (type: DescriptionTypeEnum) => void;
}

const serviceDescriptions: Array<{ name: string; type: DescriptionTypeEnum }> = [
  { name: 'PETKIT Care+ 服务介绍', type: DescriptionTypeEnum.INTRODUCTION },
  {
    name: 'PETKIT Care+ 服务说明协议',
    type: DescriptionTypeEnum.DESCRIPTION_PROTOCOL,
  },
  { name: '自动续费服务协议', type: DescriptionTypeEnum.AUTO_RENEW_PROTOCOL },
];

const Protocol: React.FC<ProtocolProps> = ({ onNavigate }) => {
  const handleDescriptionNavigation = (type: DescriptionTypeEnum) => {
    if (onNavigate) {
      onNavigate(type);
    } else {
      // 默认导航逻辑
      history.push(`/description/${type}?hasNavBar=1`);
    }
  };

  return (
    <section>
      <div className="flex mb-4">
        <h3
          className="text-xl font-bold"
          onClick={() => {
            global.gotoCurrentDeviceServicePageWithCloseWebview();
          }}
        >
          服务说明
        </h3>
      </div>
      <div className="flex flex-col">
        {serviceDescriptions.map((item) => (
          <div
            key={item.name}
            className="flex justify-between items-center text-sm active-color mb-4.5"
            onClick={() => handleDescriptionNavigation(item.type)}
          >
            {item.name} <RightOutline />
          </div>
        ))}
      </div>
    </section>
  );
};

export default Protocol;