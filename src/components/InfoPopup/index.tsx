import { Image, Popup } from 'antd-mobile';

interface Props {
  visible: boolean;
  title: string;
  content: string;
  imageUrl: string;
  onClose?: () => void;
}

const InfoPopup = ({ visible, title, content, imageUrl, onClose }: Props) => {
  return (
    <Popup
      visible={visible}
      onMaskClick={() => {
        onClose && onClose();
      }}
      bodyClassName="w-auto rounded-xl m-4"
      // bodyStyle={{ margin: '0 0 16px' }}
    >
      <div
        className="w-full flex justify-center"
        style={{
          borderRadius: '0.75rem 0.75rem 0 0',
          overflow: 'hidden',
          background: '#f7f7f7',
        }}
      >
        <Image className="rounded-t-xl" width="100%" src={imageUrl} fit="contain" />
      </div>
      <div className="flex flex-col justify-center items-center p-4 pb-8">
        <div className="text-1.5xl normal-color mb-3 font-bold">{title}</div>
        <div className="text-sm default-color text-center">{content}</div>
      </div>
    </Popup>
  );
};

export default InfoPopup;
