.container {
  position: relative;
  border-radius: 16px;
}

.overlay {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.playContainer {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  position: absolute;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.playIcon {
  transform: rotate(-90deg);
  font-size: 2rem;
}

.video {
  border-radius: 16px;
}
