import { DownFill } from 'antd-mobile-icons';
import classNames from 'classnames';
import React, { useRef, useState } from 'react';
import styles from './index.less';

type Props = React.DetailedHTMLProps<
  React.VideoHTMLAttributes<HTMLVideoElement>,
  HTMLVideoElement
>;

const Video: React.FC = React.forwardRef(
  ({ className = '', controls, onPlay, onPause, ...rest }: Props, _) => {
    const ref = useRef<HTMLVideoElement>(null);
    const [hasControl, setHasControl] = useState(false);
    const [showOverlay, setShowOverlay] = useState(true);

    const play = () => {
      ref.current?.play();
    };

    const onVideoPlay = (ev: React.SyntheticEvent<HTMLVideoElement, Event>) => {
      ref.current?.requestFullscreen();
      setHasControl(controls || false);
      setShowOverlay(false);
      if (onPlay) onPlay(ev);
    };

    const onVideoPause = (
      ev: React.SyntheticEvent<HTMLVideoElement, Event>,
    ) => {
      if (onPause) onPause(ev);
    };

    return (
      <div className={styles.container}>
        {showOverlay ? (
          <div className={styles.overlay} onClick={play}>
            <div className={styles.playContainer}>
              <DownFill className={styles.playIcon} />
            </div>
          </div>
        ) : null}
        <video
          ref={ref}
          className={classNames(styles.video, className)}
          controls={hasControl}
          controlsList="nodownload"
          onPlay={onVideoPlay}
          onPause={onVideoPause}
          {...rest}
        />
      </div>
    );
  },
);

export default Video;
