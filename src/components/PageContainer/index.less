.container {
    padding-bottom: 50px;

    &::-webkit-scrollbar {
        display: none;
    }

    :global(.adm-nav-bar-right) {
        display: flex;
        justify-content: flex-end;
    }
}

.rightActionPopover {

    :global(.adm-popover-inner) {
        width: 100px;
        overflow: hidden;
        box-shadow: 0 0 30px 0 rgba(51, 51, 51, 0.2) !important;
    }

    :global(.adm-popover-arrow) {
        display: none;
    }

    :global(.adm-popover-menu-item) {
        padding-left: 0;
        display: block;
    }

    :global(.adm-popover-menu-item-text) {
        font-size: 12px;
        color: #333;
        text-align: center;
        padding: 10px 0;
        width: 100px;
        line-height: 1.2em;
    }
}

.rightMenuButton {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20px;

    &:hover {
        cursor: pointer;
    }

    &Dot {
        width: 4px;
        height: 4px;
        background: #000;
        margin-bottom: 3px;
        border-radius: 50%;
    }
}