import global from '@/utils/global';
import { NavBar, Popover } from 'antd-mobile';
import { AntOutline } from 'antd-mobile-icons';
import { Action } from 'antd-mobile/es/components/popover';
import classNames from 'classnames';
import React, { useState } from 'react';
import styles from './index.less';
import { PageContainerProps } from './interface';

const PageContainer: React.FC<PageContainerProps> = ({
  title = 'PETKIT Care+',
  onBack,
  rightMenuActions = [],
  enableDebug = false,
  className,
  scrollRef,
  children,
}) => {
  const [debugCount, setDebugCount] = useState(0);

  // 处理标题点击事件（调试功能）
  const handleTitleClick = () => {
    if (!enableDebug) return;

    const newCount = debugCount + 1;
    setDebugCount(newCount);

    if (newCount >= 3) {
      global.setupVConsole();
    }
  };

  // 渲染右侧内容
  const renderRightContent = () => {
    const showDebugIcon = enableDebug && debugCount >= 3;
    const hasMenuActions = rightMenuActions.length > 0;

    if (!showDebugIcon && !hasMenuActions) {
      return null;
    }

    return (
      <>
        {showDebugIcon && (
          <AntOutline
            style={{ marginRight: 20 }}
            fontSize={20}
            onClick={global.setupVConsole}
          />
        )}
        {hasMenuActions && (
          <Popover.Menu
            actions={rightMenuActions as Action[]}
            placement="bottom-end"
            trigger="click"
            className={styles.rightActionPopover}
          >
            <div className={styles.rightMenuButton}>
              <div className={styles.rightMenuButtonDot} />
              <div className={styles.rightMenuButtonDot} />
              <div className={styles.rightMenuButtonDot} />
            </div>
          </Popover.Menu>
        )}
      </>
    );
  };

  // 生成容器样式类名
  const containerClassName = classNames(
    styles.container,
    ' h-full overflow-auto relative',
    {
      'padding-safe-area-ios': global.isiOS(),
      'padding-safe-area-android': global.isAndroid(),
      'padding-safe-area-harmony': global.isHarmony(),
    },
    className,
  );

  return (
    <div className={containerClassName} ref={scrollRef}>
      <NavBar right={renderRightContent()} onBack={onBack}>
        <span
          className="active-color font-bold text-lg"
          onClick={handleTitleClick}
        >
          {title}
        </span>
      </NavBar>
      {children}
    </div>
  );
};

export default PageContainer;
