import { Action } from 'antd-mobile/es/components/popover';

export interface PageContainerProps {
  /** 页面标题 */
  title?: string;
  /** 返回按钮点击事件 */
  onBack?: () => void;
  /** 右侧菜单操作项 */
  rightMenuActions?: Action[];
  /** 是否启用调试功能 */
  enableDebug?: boolean;
  /** 自定义容器样式类名 */
  className?: string;
  /** 滚动容器引用 */
  scrollRef?: React.RefObject<HTMLDivElement>;
  /** 子组件内容 */
  children: React.ReactNode;
}
