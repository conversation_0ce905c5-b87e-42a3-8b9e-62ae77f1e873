export enum EnterEnum {
  FULL_MUKBANG_VIDEO = 'Channel1',
  MINE = 'Channel2',
  SERVICE_MANAGEMENT = 'Channel3',
  INDEX = 'Channel4',
  HORIZONTAL_MUKBANG_VIDEO = 'Channel5',
  VLOG = 'Channel6',
  UNKNOWN = 'unknown',
}

export enum ReNewEnum {
  RENEW = 1,
  NOT_RENEW = 0,
}

export interface BreadcrumbInfo {
  path: string;
  breadcrumbName: string;
}

export type ValueType = string | number;

export interface SelectOption<T = ValueType> {
  label: string;
  value: T;
}

export interface Key {
  key: string;
}

export enum StatusEnum {
  ENABLE = 1,
  DISABLE = 0,
}

export enum UrlStatusEnum {
  ENABLE = '1',
  DISABLE = '0',
}

export type ProxyType = 'online' | 'sandbox';

export enum NumberBooleanEnum {
  TRUE = 1,
  FALSE = 0,
}
