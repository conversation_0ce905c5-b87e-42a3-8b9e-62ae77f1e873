import { ReNewEnum } from '../common.interface';
import {
  ServiceTimeUnitEnum,
  SuitableDeviceTypeEnum,
} from '../product/interface';
import {
  DeviceInfoWithPackage,
  DevicePackageInfo,
  DeviceServiceProductStatusEnum,
  DeviceServiceStatusEnum,
} from './interface';

export const initDevicePackageInfo: DevicePackageInfo = {
  id: 0,
  serviceId: 0,
  skuId: 0,
  skuName: '',
  skuShortName: '',
  skuLevel: 0,
  /**
   * @description 生效时间戳
   */
  workTime: 0,
  /**
   * @description 到期时间戳
   */
  workIndate: 0,
  subscribe: ReNewEnum.NOT_RENEW,
  productStatus: DeviceServiceProductStatusEnum.OUT_DATE,
  // 周期数
  serviceTime: 1,
  // 周期单位
  serviceTimeUnit: ServiceTimeUnitEnum.DAY,
};

export const initDeviceInfoWithPackage: DeviceInfoWithPackage = {
  userId: 0,
  deviceId: 0,
  deviceName: '',
  deviceUrl: '',
  status: DeviceServiceStatusEnum.NO_SERVICE,
  deviceType: SuitableDeviceTypeEnum.D4sh,
  effectiveProductInfo: initDevicePackageInfo,
  expiredProductInfos: [],
  pendingProductInfos: [],
};
