import { Effect, Reducer } from '@umijs/max';
import { fetchDeviceInfoWithPackage } from './fetch';
import { DeviceInfoWithPackage } from './interface';

export interface DeviceState {
  deviceInfo?: DeviceInfoWithPackage;
}

export const initDeviceState: DeviceState = {
  deviceInfo: undefined,
  // deviceInfo: {
  //   userId: 1231231,
  //   deviceId: 2,
  //   deviceName: '测试一下',
  //   deviceUrl: '',
  //   status: DeviceServiceStatusEnum.NO_SERVICE,
  //   deviceType: SuitableDeviceTypeEnum.D4sh,
  //   effectiveProductInfo: null,
  //   expiredProductInfos: [],
  //   pendingProductInfos: [],
  //   sn: 'sdlkfjasdkl',
  //   region: 'dslkfj',
  // },
};

export interface DeviceModel {
  namespace: 'device';
  state: DeviceState;
  effects: {
    requestDeviceInfoWithPackage: Effect;
  };
  reducers: {
    requestDeviceInfoWithPackageSuccess: Reducer<
      DeviceState,
      {
        type: 'requestDeviceInfoWithPackageSuccess';
        payload: DeviceInfoWithPackage;
      }
    >;
  };
}

const device: DeviceModel = {
  namespace: 'device',
  state: initDeviceState,
  effects: {
    *requestDeviceInfoWithPackage({ payload }, { call, put }) {
      const deviceInfo: DeviceInfoWithPackage = yield call(
        fetchDeviceInfoWithPackage,
        payload,
      );
      // deviceInfo.pendingProductInfos = [];
      yield put({
        type: 'requestDeviceInfoWithPackageSuccess',
        payload: {
          ...deviceInfo,
          deviceId: payload.deviceId,
          deviceUrl: (deviceInfo.deviceUrl || '')
            .replace('http://', 'https://')
            .replace('sandbox.', 'sandbox-'),
        },
      });
    },
  },
  reducers: {
    requestDeviceInfoWithPackageSuccess(
      state = initDeviceState,
      { payload },
    ): DeviceState {
      return {
        ...state,
        deviceInfo: payload,
      };
    },
  },
};

export default device;
