import { ReNewEnum } from '../common.interface';
import {
  ServiceTimeUnitEnum,
  SuitableDeviceTypeEnum,
} from '../product/interface';

export enum DeviceServiceStatusEnum {
  NO_SERVICE = 0,
  SERVICE_EFFECTIVE = 1,
  SERVICE_OUT_DATE = 2,
}

export enum DeviceServiceProductStatusEnum {
  EFFECTIVE = 1,
  OUT_DATE = 2,
  WAIT_EFFECTIVE = 3,
}

export interface DeviceInfoWithPackageParam {
  deviceId: number;
  deviceType: SuitableDeviceTypeEnum;
}

export interface DeviceInfoWithPackage {
  userId: number;
  deviceId: number;
  deviceName: string;
  deviceUrl: string;
  status: DeviceServiceStatusEnum;
  deviceType: SuitableDeviceTypeEnum;
  effectiveProductInfo: DevicePackageInfo | null;
  expiredProductInfos: DevicePackageInfo[];
  pendingProductInfos: DevicePackageInfo[];
  zoneId: string;
}

export interface DevicePackageInfo {
  id: number;
  serviceId: number;
  skuId: number;
  skuName: string;
  skuShortName: string;
  skuLevel: number;
  /**
   * @description 生效时间戳
   */
  workTime: number;
  /**
   * @description 到期时间戳
   */
  workIndate: number;
  subscribe: ReNewEnum;
  productStatus: DeviceServiceProductStatusEnum;
  // 周期数
  serviceTime: number;
  // 周期单位
  serviceTimeUnit: ServiceTimeUnitEnum;
}
