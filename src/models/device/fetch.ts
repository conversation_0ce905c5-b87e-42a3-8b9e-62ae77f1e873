import { deviceApiOption } from '@/services/api/device';
import request from '@/utils/request';
import { DeviceInfoWithPackage, DeviceInfoWithPackageParam } from './interface';

// 获取Device列表
export const fetchDeviceInfoWithPackage = (
  param: DeviceInfoWithPackageParam,
): Promise<DeviceInfoWithPackage> => {
  const config = deviceApiOption.deviceInfoWithPackage;
  config.option.params = param;
  return request(config.url, config.option);
};
