import { MAX_REST_DAYS } from '../common.util';
import { DeviceInfoWithPackage } from '../device/interface';
import { ProductSku, ServiceTimeUnitEnum } from './interface';

export const productService = {
  getPaymentPrice: (
    deviceInfo?: DeviceInfoWithPackage,
    productSku?: ProductSku,
  ) => {
    if (!productSku || !deviceInfo) return 0;
    // 自动续费套餐不存在活动
    if (productSku.actPackage && productSku.actPackage.price)
      return productSku.actPackage.price.amount;
    if (!productSku.price.isReNew) return productSku.price.price;
    if (
      deviceInfo.effectiveProductInfo &&
      deviceInfo.effectiveProductInfo.skuId === productSku.id
    ) {
      return productSku.price.price;
    }

    if (
      productSku.price.firstPhasePrice !== undefined &&
      productSku.price.firstPhasePrice !== null
    ) {
      return productSku.price.firstPhasePrice || 0;
    }
    return productSku.price.price;
  },
  getSkuServiceTimeUnit: (
    serviceTime?: number,
    serviceTimeUnit?: ServiceTimeUnitEnum,
  ) => {
    if (
      (serviceTime === 12 && serviceTimeUnit === ServiceTimeUnitEnum.MONTH) ||
      (serviceTime === 1 && serviceTimeUnit === ServiceTimeUnitEnum.YEAR)
    ) {
      // 包1年
      return ServiceTimeUnitEnum.YEAR;
    } else if (
      (serviceTime === 1 && serviceTimeUnit === ServiceTimeUnitEnum.MONTH) ||
      (serviceTime === MAX_REST_DAYS &&
        serviceTimeUnit === ServiceTimeUnitEnum.DAY)
    ) {
      // 包1月 每月按30天来算
      return ServiceTimeUnitEnum.MONTH;
    } else if (
      serviceTime === 1 &&
      serviceTimeUnit === ServiceTimeUnitEnum.DAY
    ) {
      // 包1天 一天
      return ServiceTimeUnitEnum.DAY;
    }
  },
};
