import { Dayjs } from 'dayjs';
import { NumberBooleanEnum, StatusEnum } from '../common.interface';

export enum SaleStatusEnum {
  ON_SALE = 1,
  OFF_SALVE = 0,
}

export enum ProductSkuCapacityTypeEnum {
  CLOUD_STORAGE = 'CLOUD_STORAGE',
  SPLENDID_MOMENT = 'SPLENDID_MOMENT',
  TIME_LAPSE = 'TIME_LAPSE',
}

export enum SkuTypeEnum {
  EVENT = 'EVENT',
  CVR = 'CVR',
  BASIC = 'BASIC',
}

export enum RecordTypeEnum {
  EVENT = 'EVENT',
  CVR = 'CVR',
}

export enum ServiceTimeUnitEnum {
  YEAR = 'YEAR',
  MONTH = 'MONTH',
  DAY = 'DAY',
}

export enum SuitableDeviceTypeEnum {
  D4sh = 'D4sh',
  D4h = 'D4h',
  T5 = 'T5',
  T6 = 'T6',
  T7 = 'T7',
}

export enum ExtraServiceTimeUnitEnum {
  SEASON = 'SEASON',
}

// 活动生效时间类型枚举
export enum ActTimeEnum {
  CUSTOM = 'CUSTOM',
  FOREVER = 'FOREVER',
}

// 等级枚举
export enum LevelEnum {
  MINIMUE = 4,
  BASIC_MINUS = 3,
  BASIC = 2,
  PREMIUM = 1,
  PREMIUM_PLUS = 0,
}

// export enum ChargeTypeEnum {
//   CHARGE = 'CHARGE',
//   FREE = 'FREE',
// }

export interface SuitableProductListParam {
  deviceType: SuitableDeviceTypeEnum;
  deviceId: number;
}

export interface ProductSkuDetailParam {
  deviceType: SuitableDeviceTypeEnum;
  deviceId: number;
  skuId: number;
}

export interface Currency {
  countryCode: string;
  countryName: string;
  currencySymbol: string;
  currencyCode: string;
  id: number;
}

// 列表数据
export interface ProductSku {
  id: number;
  name: string;
  shortName: string;
  aliasName: string;
  saleStatus: boolean;
  level: LevelEnum;
  /**
   * @deprecated capacities 已废弃
   */
  capacities: Array<{
    type: ProductSkuCapacityTypeEnum;
    recordType: RecordTypeEnum;
  }>;
  /**
   * @deprecated cycleTime 已废弃
   */
  cycleTime: number;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  deviceTypes: SuitableDeviceTypeEnum[];
  price: {
    price: number;
    linePrice: number;
    firstPhasePrice?: number;
    isReNew: boolean;
    currency: Currency;
  };
  cornerMarkIcon: string;
  description: string;
  relationSkuId: number;
  benefits: Benefit[];
  benefitsJsonStr: string;
  actPackage?: ProductSkuActivityInfo;
  createTime: number;
}

export interface BenefitListParam {
  capacities: Array<{
    type: ProductSkuCapacityTypeEnum;
    recordType: RecordTypeEnum;
  }>;
  cycleTime: number;
}

export interface Benefit {
  id: number;
  name: string;
  icon: string;
  description: string;
  image: string;
  isCoreBenefit: StatusEnum;
  attributeText: string;
  attributeSelectedText: string;
  deviceType: string;
  sort: number;
  handpick: boolean;
}

// SKU身上的活动数据
export interface ProductSkuActivityInfo {
  actPackageId: number;
  cornerMarkIcon: string;
  effectTime: string;
  inEffectTime: string;
  actSkuName: string;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  // actPrice: number;
  actTimeEnum: ActTimeEnum;
  price: { amount: number; currency: Currency };
}

export interface BenefitCompare {
  id: number;
  image: string;
  deviceType: SuitableDeviceTypeEnum;
}

// 用于展示在confirm上的套餐信息数据结构
export interface ConfirmPackageSkuInfo {
  name: string;
  start: Dayjs;
  end: Dayjs;
  orderType: string;
  price: number;
  linePrice?: number;
  priceCurrencySymbol: string;
}

/**
 * ==========================================
 * benefit用数据结构 start
 */
export interface BenefitDetail {
  image: string;
  highlight: string;
  playback1080p: string;
  vlog: string;
  displayTheme?: string;
  fecesPic?: string;
  healthyAnalysis?: string;
}

export type LevelSkuGroup = {
  [key in LevelEnum]: {
    [key in ServiceTimeUnitEnum]: ProductSku[];
  };
};
/**
 * benefit用数据结构 end
 * ==========================================
 */

/**
 * ==========================================
 * 默认权益列表相关数据结构 start
 */
export interface CloudStorageBenefit {
  // 权益ID
  id: number;
  groupId: string;
  // 权益名称
  name: string;
  icon: string;
  // 描述
  description: string;
  // 图片
  image: string;
  // 介绍
  introduction: string;
  // 是否核心权益
  isCoreBenefit: NumberBooleanEnum;
  // 是否默认属性值
  isDefaultAttribute: number;
  // 属性类型
  attributeType: number;
  // 属性值
  attributeText: string;
  // 精选权益的属性值
  attributeSelectedText: string;
  // 设备类型
  deviceType: string;
  // 排序
  sort: number;
}

export interface DefaultBenefitListResponse {
  result: CloudStorageBenefit[];
}
/**
 * 默认权益列表相关数据结构 end
 * ==========================================
 */

/**
 * ==========================================
 * 组合SKU相关数据结构 start
 */
export interface BundleSkuListParam {
  serviceTimeUnit?: ServiceTimeUnitEnum;
}

export interface BundleSkuPrice {
  price: number;
  linePrice: number;
  unitPrice: number;
  currency: {
    currencyCode: string;
    currencyName: string;
    currencySymbol: string;
    minimumAmount: number;
    paymentMethods: Array<{
      paymentPlatform: string;
    }>;
    paymentPlatform: string[];
    country: Array<{
      countryCode: string;
      countryName: string;
    }>;
  };
  firstPhasePrice: number;
  isReNew: number;
}

export interface BundleSku {
  id: number;
  name: string;
  shortName: string;
  aliasName: string;
  /** 优惠名称 - 用于显示套餐的优惠信息 */
  discountName: string;
  /** 支持设备数量 - 套餐支持绑定的设备数量 */
  deviceNumber: number;
  serviceTime: number;
  serviceTimeUnit: string;
  cycleTime: number;
  price: BundleSkuPrice;
  cornerMarkIcon: string;
  level: number;
  sort: number;
  /** @deprecated 关联SKU列表字段已废弃，不再使用 */
  // skuIdList: number[];
  saleStatus: number;
}

/**
 * 组合SKU相关数据结构 end
 * ==========================================
 */
