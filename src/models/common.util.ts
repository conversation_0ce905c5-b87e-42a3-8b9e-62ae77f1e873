import { SuitableDeviceTypeEnum } from './product/interface';

// 非订阅补差价订阅前的剩余天数判断
export const MAX_REST_DAYS = 35;

export const getTwoDigitNumber = (number: number): string =>
  `${number >= 0 && number < 10 ? '0' : ''}${number}`;

export function capitalizeFirstLetter(word: string): SuitableDeviceTypeEnum {
  return `${word.charAt(0).toUpperCase()}${word.slice(
    1,
  )}` as SuitableDeviceTypeEnum;
}
