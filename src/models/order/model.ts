import { Effect, Reducer } from '@umijs/max';
import { fetchOrderCalc } from './fetch';
import { OrderCalcResult } from './interface';

export interface OrderState {
  orderCalcResult?: OrderCalcResult;
}

export const initOrderState: OrderState = {};

export interface OrderModel {
  namespace: 'order';
  state: OrderState;
  effects: {
    requestOrderCalc: Effect;
  };
  reducers: {
    requestOrderCalcSuccess: Reducer<
      OrderState,
      { type: 'requestOrderCalcSuccess'; payload: OrderCalcResult }
    >;
  };
}

const orderModel: OrderModel = {
  namespace: 'order',
  state: initOrderState,
  effects: {
    *requestOrderCalc({ payload }, { call, put }) {
      const result: OrderCalcResult = yield call(fetchOrderCalc, payload);
      yield put({
        type: 'requestOrderCalcSuccess',
        payload: result,
      });
    },
  },
  reducers: {
    requestOrderCalcSuccess(state = initOrderState, { payload }): OrderState {
      return {
        ...state,
        orderCalcResult: payload,
      };
    },
  },
};

export default orderModel;
