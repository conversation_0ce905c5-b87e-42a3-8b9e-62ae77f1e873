import { initPaginatorParam } from '@/utils/request';
import {
  OrderCalcResult,
  OrderListParam,
  OrderStatusEnum,
  PayTypeEnum,
  SubscriptionOrderCalcResult,
} from './interface';

export const initOrderListParam: OrderListParam = {
  ...initPaginatorParam,
};

export const orderStatusObj: { [key in OrderStatusEnum]: string } = {
  [OrderStatusEnum.NONE]: '未知状态',
  [OrderStatusEnum.CREATED]: '已创建',
  [OrderStatusEnum.PAYING]: '待支付',
  [OrderStatusEnum.PAY_SUCCEED]: '付款成功',
  [OrderStatusEnum.PAY_FAILED]: '付款失败',
  [OrderStatusEnum.REFUND_FAILED]: '退款失败',
  [OrderStatusEnum.REFUNDING]: '退款中',
  [OrderStatusEnum.REFUND_SUCCEED]: '已退款',
  [OrderStatusEnum.CANCELLED]: '已取消',
  [OrderStatusEnum.CANCELLED_SYSTEM]: '已取消',
  [OrderStatusEnum.FINISHED]: '已完结',
};

export const payTypeObj: { [key in PayTypeEnum]: string } = {
  [PayTypeEnum.ALIPAY]: '支付宝',
  [PayTypeEnum.WEIXIN]: '微信',
  [PayTypeEnum.EXPERIENCE]: '活动领取',
  [PayTypeEnum.EXCHANGECOUPON]: '卡券激活',
};

export const transferSubscriptionResultToOrderResult = (
  result: SubscriptionOrderCalcResult,
): OrderCalcResult => {
  const orderCalcResult: OrderCalcResult = {
    currentServiceProduct: result.currentProduct,
    purchaseServiceProduct: result.signProduct,
    upgradeServiceProduct: result.upgradeProduct,
    // 是否能购买
    purchase: result.sign,
    purchaseAmount: result.signAmount,
    // 是否能补差价
    upgrade: result.upgrade,
    upgradeAmount: result.upgradeAmount,
  };
  return orderCalcResult;
};
