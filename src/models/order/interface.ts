import { PaginatorParam } from '@/utils/request';
import { NumberBooleanEnum } from '../common.interface';
import { Currency, SuitableDeviceTypeEnum } from '../product/interface';

export enum OrderStatusEnum {
  /**
   * 初始状态
   */
  NONE = 'NONE',
  /**
   * 已创建
   */
  CREATED = 'CREATED',
  /**
   * 付款中
   */
  PAYING = 'PAYING',
  /**
   * 支付成功
   */
  PAY_SUCCEED = 'PAY_SUCCEED',
  /**
   * 支付失败
   */
  PAY_FAILED = 'PAY_FAILED',
  /**
   * 退款失败
   */
  REFUND_FAILED = 'REFUND_FAILED',
  /**
   * 退款中
   */
  REFUNDING = 'REFUNDING',
  /**
   * 已退款
   */
  REFUND_SUCCEED = 'REFUND_SUCCEED',
  /**
   * 已取消
   */
  CANCELLED = 'CANCELLED',
  /**
   * 系统取消
   */
  CANCELLED_SYSTEM = 'CANCELLED_SYSTEM',
  /**
   * 订单已完结, 不可再次操作
   */
  FINISHED = 'FINISHED',
}

export enum SubscriptionStateEnum {
  /**
   * 签约中
   */
  SIGNING = 'Signing',
  /**
   * 可用
   */
  ACTIVITY = 'Activity',
  /**
   * 已解约
   */
  DISABLED = 'Disabled',
  /**
   * 已取消
   */
  CANCELLED = 'Cancelled',
}

export enum PayTypeEnum {
  /**
   * 支付宝
   */
  ALIPAY = 'Alipay',
  /**
   * 微信支付
   */
  WEIXIN = 'Weixin',
  /**
   * 免费领取
   */
  EXPERIENCE = 'Experience',
  /**
   * 卡券激活
   */
  EXCHANGECOUPON = 'ExchangeCoupon',
}

// 列表参数数据
export type OrderListParam = PaginatorParam;

export interface Order {
  id: number;
  userId: number;
  orderId: string;
  state: OrderStatusEnum;
  skuName: string;
  couponNickName: string;
  amount: number;
  refundedAmount: number;
  platform: PayTypeEnum;
  payExpireTime: number;
  createTime: number;
  isRenew: boolean;
  upgrade: NumberBooleanEnum;
  device: {
    deviceId: number;
    deviceType: SuitableDeviceTypeEnum;
  };
  currencyInfo: Pick<Currency, 'currencyCode' | 'currencySymbol'>;
}

// 创建订单的参数
export interface OrderCreationParam {
  deviceId: number;
  deviceType: SuitableDeviceTypeEnum;
  skuId: number;
  // 微信支付需要userId
  // userId?: number;
  platform: PayTypeEnum;
  upgrade: NumberBooleanEnum;
}

export interface OrderCreationV2Param {
  deviceId: number;
  deviceType: SuitableDeviceTypeEnum;
  skuId: number;
  platform: PayTypeEnum;
  upgrade: NumberBooleanEnum;
  activityId?: number;
}

// 创建订单的返回参数
export interface OrderCreationResult {
  platform: PayTypeEnum;
  orderId: string;
  tradeNo: string;
  amount: number;
  skuName: string;
  payload: string;
  deviceId?: number;
  deviceType?: SuitableDeviceTypeEnum;
  state: OrderStatusEnum;
}

export interface SubscriptionResult {
  subNo: string;
  payload: string;
  deviceId?: number;
  deviceType?: SuitableDeviceTypeEnum;
}

// 同步支付订单的结果
export interface AsyncOrder {
  id: number;
  userId: number;
  orderId: string;
  state: OrderStatusEnum;
  skuName: string;
  device: { deviceId: number; deviceType: SuitableDeviceTypeEnum };
  amount: number;
  platform: PayTypeEnum;
  payExpireTime: number;
  createTime: number;
}

export interface AsyncSubscriptionInfoParam {
  subNo: string;
  agreementNo?: string;
}

// 同步签约结果的结果参数
export interface AsyncSubscriptionInfo {
  subNo: string;
  state: SubscriptionStateEnum;
  payExpireTime: string;
}

export interface ResultProductInfo {
  skuId: number;
  skuName: string;
  workInDate: number;
  workTime: number;
  amount: number;
  // 剩余价格
  remainAmount: number;
  currency: Currency;
  activity: {
    actName: string;
  };
}

/**
 * ================================================
 * @description 订单费用计算参数与结果
 */
export interface OrderCalcParam {
  deviceId: number;
  deviceType: SuitableDeviceTypeEnum;
  skuId: number;
  // platform: PayTypeEnum;
  activityId?: number;
}

export interface OrderCalcResult {
  currentServiceProduct: ResultProductInfo;
  purchaseServiceProduct: ResultProductInfo;
  upgradeServiceProduct?: ResultProductInfo;
  // 是否能购买
  purchase: NumberBooleanEnum;
  purchaseAmount: number;
  // 是否能补差价
  upgrade: NumberBooleanEnum;
  upgradeAmount: number;
}
/**
 * ================================================
 */

/**
 * ================================================
 * @description 订单费用计算参数与结果
 */
export interface SubscriptionOrderCalcParam {
  skuId: number;
  deviceId: number;
  deviceType: SuitableDeviceTypeEnum;
}

export interface SubscriptionOrderCalcResult {
  currentProduct: ResultProductInfo;
  signProduct: ResultProductInfo;
  upgradeProduct?: ResultProductInfo;
  // 是否能签约
  sign: NumberBooleanEnum;
  signAmount: number;
  // 是否补差价
  upgrade: NumberBooleanEnum;
  upgradeAmount: number;
}
/**
 * ================================================
 */

/**
 * ================================================
 * @description 判断当前所选套餐是否存在未支付的情况
 */
export interface OrderCheckResult {
  [OrderStatusEnum.PAYING]: string[];
}
/**
 * ================================================
 */
