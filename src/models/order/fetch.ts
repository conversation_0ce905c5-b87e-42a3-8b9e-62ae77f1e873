import { orderApiOption } from '@/services/api/order';
import { getCurrentDomain } from '@/utils/currentDomain';
import type { Data } from '@/utils/request';
import request from '@/utils/request';
import {
  AsyncOrder,
  AsyncSubscriptionInfo,
  AsyncSubscriptionInfoParam,
  Order,
  OrderCalcParam,
  OrderCalcResult,
  OrderCheckResult,
  OrderCreationParam,
  OrderCreationResult,
  OrderCreationV2Param,
  OrderListParam,
  SubscriptionOrderCalcParam,
  SubscriptionOrderCalcResult,
  SubscriptionResult,
} from './interface';

// 获取Order列表
export const fetchOrderList = (param: OrderListParam): Promise<Data<Order>> => {
  const config = orderApiOption.orderList;
  config.option.params = param;
  return request(config.url, config.option);
};

/**
 * @deprecated
 */
export const fetchAlipayOrderCreation = (
  param: OrderCreationParam,
): Promise<string> => {
  const config = orderApiOption.alipayOrderCreation;
  config.option.params = param;
  return request(config.url, config.option);
};

/**
 * @deprecated 发起支付V1
 */
export const fetchOrderCreation = (
  param: OrderCreationParam,
): Promise<OrderCreationResult> => {
  const config = orderApiOption.orderCreation;
  config.option.params = param;
  return request(config.url, config.option);
};

/**
 * @description 发起支付V2
 */
export const fetchOrderCreationV2 = (
  param: OrderCreationV2Param,
): Promise<OrderCreationResult> => {
  const config = orderApiOption.orderCreationV2;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/json',
  };
  config.option.requestType = 'json';
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * @deprecated 再次支付订单 支付宝
 */
export const getAlipayOrderPayAgain = (orderId: string) => {
  const config = orderApiOption.orderPayAgain;
  const sessionToken = localStorage.getItem('sessionToken');
  return `${getCurrentDomain()}${
    config.url
  }?orderId=${orderId}&X-Session=${sessionToken}`;
};

/**
 * @deprecated 再次支付订单 微信
 */
export const fetchWechatOrderPayAgain = (orderId: string): Promise<string> => {
  const config = orderApiOption.orderPayAgain;
  config.option.data = { orderId };
  return request(config.url, config.option);
};

// 重新发起支付
export const fetchOrderRepay = (
  orderId: string,
): Promise<OrderCreationResult> => {
  const config = orderApiOption.orderPayAgain;
  config.option.data = { orderId };
  return request(config.url, config.option);
};

// 获取支付宝支付链接url
/**
 * @deprecated
 */
export const getAlipayUrl = (param: OrderCreationParam) => {
  const { skuId, deviceId, deviceType } = param;
  const token = localStorage.getItem('sessionToken');
  const config = orderApiOption.alipayOrderCreation;
  const url = `${location.protocol}//${getCurrentDomain()}${
    config.url
  }?X-Session=${token}&deviceId=${deviceId}&deviceType=${deviceType}&skuId=${skuId}&payPlatform=Alipay`;
  return url;
};

/**
 * @deprecated 签约支付宝 - 周期扣款
 * 获取支付宝周期支付链接url
 */
export const fetchSubscriptionSign = (
  param: OrderCreationParam,
): Promise<SubscriptionResult> => {
  const config = orderApiOption.subscriptionSign;
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * @description 签约支付宝 - 周期扣款
 * 获取支付宝周期支付链接url
 */
export const fetchSubscriptionSignV2 = (
  param: OrderCreationParam,
): Promise<SubscriptionResult> => {
  const config = orderApiOption.subscriptionSignV2;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/json',
  };
  config.option.requestType = 'json';
  config.option.data = param;
  return request(config.url, config.option);
};

// 获取微信支付链接url
/**
 * @deprecated
 */
export const fetchWeixinUrl = (param: OrderCreationParam) => {
  const config = orderApiOption.weixinOrderCreation;
  config.option.data = param;
  return request(config.url, config.option);
};

// 删除订单
export const fetchCancelOrder = (orderId: string): Promise<void> => {
  const config = orderApiOption.cancelOrder;
  config.option.data = { orderId };
  return request(config.url, config.option);
};

// 同步订单支付结果
export const fetchPaymentResult = (orderId: string): Promise<AsyncOrder> => {
  const config = orderApiOption.paymentResult;
  config.option.data = { orderId };
  return request(config.url, config.option);
};
// 获取订单详情
export const fetchOrderDetail = (orderId: string): Promise<AsyncOrder> => {
  const config = orderApiOption.orderDetail;
  config.option.params = { orderId };
  return request(config.url, config.option);
};

// 同步签约结果
export const fetchSubscribeResult = (
  param: AsyncSubscriptionInfoParam,
): Promise<AsyncSubscriptionInfo> => {
  const config = orderApiOption.subscribeResult;
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchOrderCalc = (
  param: OrderCalcParam,
): Promise<OrderCalcResult> => {
  const config = orderApiOption.orderCalc;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/json',
  };
  config.option.requestType = 'json';
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchSubscriptionOrderCalc = (
  param: SubscriptionOrderCalcParam,
): Promise<SubscriptionOrderCalcResult> => {
  const config = orderApiOption.subscriptionOrderCalc;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/json',
  };
  config.option.requestType = 'json';
  config.option.data = param;
  return request(config.url, config.option);
};

// 判断当前所选套餐是否存在未支付的情况
export const fetchOrderStateCheck = (): Promise<OrderCheckResult> => {
  const config = orderApiOption.orderStateCheck;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/json',
  };
  return request(config.url, config.option);
};
