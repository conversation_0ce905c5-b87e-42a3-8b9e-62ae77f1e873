import { Effect, Reducer } from '@umijs/max';
import { fetchSixtyFreeActivityTime } from './fetch';

export interface UtilState {
  sixtyFreeActivityTime: string;
}

export const initUtilState: UtilState = {
  sixtyFreeActivityTime: '',
};

export interface UtilModel {
  namespace: 'utils';
  state: UtilState;
  effects: {
    requestSixtyFreeActivityTime: Effect;
  };
  reducers: {
    requestSixtyFreeActivityTimeSuccess: Reducer<
      UtilState,
      { type: 'requestSixtyFreeActivityTimeSuccess'; payload: string }
    >;
    changeNeedProductListRefresh: Reducer<
      UtilState,
      {
        type: 'requestSuitableProductListSuccess';
        payload: boolean;
      }
    >;
  };
}

const utilModel: UtilModel = {
  namespace: 'utils',
  state: initUtilState,
  effects: {
    *requestSixtyFreeActivityTime(_, { call, put }) {
      const sixtyFreeActivityTime: string = yield call(
        fetchSixtyFreeActivityTime,
      );
      yield put({
        type: 'requestSixtyFreeActivityTimeSuccess',
        payload: sixtyFreeActivityTime,
      });
    },
  },
  reducers: {
    requestSixtyFreeActivityTimeSuccess(
      state = initUtilState,
      { payload },
    ): UtilState {
      return {
        ...state,
        sixtyFreeActivityTime: payload,
      };
    },
    changeNeedProductListRefresh(state = initUtilState, { payload }) {
      return { ...state, needProductListRefresh: payload };
    },
  },
};

export default utilModel;
