import { utilsApiOption } from '@/services/api/utils';
import request from '@/utils/request';
import { Utils, UtilsDetail, UtilsListParam, UtilsParam } from './interface';

// 获取Utils列表
export const fetchUtilsList = (param?: UtilsListParam): Promise<Utils[]> => {
  const config = utilsApiOption.utilsList;
  if (param) config.option.params = param;
  return request(config.url, config.option);
};

// 创建Utils
export const fetchUtilsCreation = (param: UtilsParam): Promise<void> => {
  const config = utilsApiOption.utilsCreation;
  config.option.data = param;
  return request(config.url, config.option);
};

// 更新Utils
export const fetchUtilsUpdate = (param: UtilsParam): Promise<void> => {
  const config = utilsApiOption.utilsUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

// 根据id获取Utils
export const fetchUtilsDetail = (id: number): Promise<UtilsDetail> => {
  const config = utilsApiOption.utilsDetail;
  config.option.params = { id };
  return request(config.url, config.option);
};

// 删除Utils
export const fetchUtilsDeletion = (id: number): Promise<void> => {
  const config = utilsApiOption.utilsDeletion;
  config.option.params = { id };
  return request<never>(config.url, config.option);
};

// 获取Util列表
export const fetchSixtyFreeActivityTime = (): Promise<string> => {
  const config = utilsApiOption.getSixtyFreeActivityTime;
  return request(config.url, config.option);
};
