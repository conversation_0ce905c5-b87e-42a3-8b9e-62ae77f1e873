variables:
  GIT_STRATEGY: fetch
stages:
  - build

build-bs-mobile-web:
  stage: build
  artifacts:
    name: '$CI_COMMIT_REF_NAME'
    paths:
      - dist
  cache:
    untracked: true
    paths:
      - node_modules
  before_script:
    - pnpm install
  script:
    - pnpm build
  only:
    - /^t.*$/
    - /^d.*$/
    - master
    - prod
    - oversea_prod
    - oversea-test
    - test-develop
  tags:
    - build-bs-mobile-web
# test runner
