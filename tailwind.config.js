module.exports = {
  important: true,
  content: [
    './src/pages/**/*.tsx',
    './src/components/**.tsx',
    './src/layouts/**.tsx',
  ],
  theme: {
    extend: {
      fontSize: {
        '0.5xl': '1.125rem',
        '1.5xl': '1.375rem',
        '2.5xl': '1.875rem',
        '3.5xl': '2.125rem',
      },
      spacing: {
        1.5: '0.375rem',
        2.5: '0.625rem',
        3.5: '0.875rem',
        4.5: '1.125rem',
        5.5: '1.375rem',
        7.5: '1.875rem',
        13: '3.25rem',
        33: '8.125rem',
        '78.x': '78px',
      },
      borderWidth: {
        3: '3px',
      },
      borderRadius: {
        half: '50%',
      },
      inset: {
        '-3.x': '-3px',
        '-11.x': '-11px',
      },
      maxWidth: {
        80: '5rem',
      },
      minWidth: {
        80: '5rem',
      },
      scale: {
        80: '0.8',
        85: '0.85',
        90: '0.9',
      },
    },
  },
};
